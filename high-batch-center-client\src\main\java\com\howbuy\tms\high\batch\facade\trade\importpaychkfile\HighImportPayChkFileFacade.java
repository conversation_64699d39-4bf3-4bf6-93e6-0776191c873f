/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.facade.trade.importpaychkfile;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @description:私募导入支付对账文件
 * <AUTHOR>
 * @date 2017年7月6日 下午4:59:53
 * @since JDK 1.6
 */
public interface HighImportPayChkFileFacade extends BaseFacade<HighImportPayChkFileRequest, HighImportPayChkFileResponse> {

}

<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.boot</groupId>
    <artifactId>howbuy-boot-actuator-parent</artifactId>
    <packaging>pom</packaging>
    <version>2.3.0-RELEASE</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <com.howbuy.howbuy-cachemanagement.version>4.4.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-message.version>3.1.0-RELEASE</com.howbuy.howbuy-message.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <spring-boot.version>2.3.12.RELEASE</spring-boot.version>
        <dubbo.version>2.7.15</dubbo.version>
        <nacos-config-spring-boot-starter.version>0.2.10</nacos-config-spring-boot-starter.version>
        <com.howbuy.utils.version>1.0.0-SNAPSHOT</com.howbuy.utils.version>
        <com.howbuy.howbuy-boot-actuator-parent.version>2.3.0-RELEASE</com.howbuy.howbuy-boot-actuator-parent.version>
    <com.howbuy.howbuy-cachemanagement-statistic.version>4.5.0-RELEASE</com.howbuy.howbuy-cachemanagement-statistic.version>
</properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config-spring-boot-starter.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message.version}</version>
                <scope>provided</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement-statistic</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement-statistic.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>${com.howbuy.utils.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                </exclusions>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://nexus.howbuy.pa/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://nexus.howbuy.pa/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
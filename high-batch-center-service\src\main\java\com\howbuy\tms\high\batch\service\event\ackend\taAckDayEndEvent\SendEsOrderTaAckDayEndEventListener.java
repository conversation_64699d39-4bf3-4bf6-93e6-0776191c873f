package com.howbuy.tms.high.batch.service.event.ackend.taAckDayEndEvent;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.ProductTypeEsSysCodeMappingEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.message.topic.MessageBody;
import com.howbuy.tms.high.batch.dao.po.order.EsProOrderMonitorPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.service.business.message.MsgNotifySendService;
import com.howbuy.tms.high.batch.service.event.HighEventListener;
import com.howbuy.tms.high.batch.service.repository.EsProOrderMonitorRepository;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:ta确认处理日终发送es订单消息
 * @Author: yun.lu
 * Date: 2024/7/3 17:18
 */
@Component
public class SendEsOrderTaAckDayEndEventListener extends HighEventListener<TaAckDayEndEvent> {
    private static final Logger logger = LogManager.getLogger(SendEsOrderTaAckDayEndEventListener.class);
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;
    @Autowired
    private EsProOrderMonitorRepository esProOrderMonitorRepository;
    @Autowired
    private MsgNotifySendService msgNotifySendService;

    @Override
    public void processEvent(TaAckDayEndEvent event) {
        logger.info("SendEsOrderTaAckDayEndEventListener-ta确认处理日终发送es订单消息-start,event={}",JSON.toJSONString(event));
        sendBatchAckKafka(event.getTradeDt(), event.getTaCode());
        logger.info("SendEsOrderTaAckDayEndEventListener-ta确认处理日终发送es订单消息-end,event={}",JSON.toJSONString(event));
    }

    private void sendBatchAckKafka(String tradeDt, String taCode) {
        try {
            int pageNo = 1;
            int pageSize = 200;
            long totalSize = 0;
            long alreadyCollectSize = -1;
            while (alreadyCollectSize < totalSize) {
                Page<SimuFundCheckOrderPo> list = simuFundCheckOrderRepository.getSimuFundCheckOrderWithPage(taCode, tradeDt, pageNo, pageSize);
                totalSize = list.getTotal();
                pageNo++;
                List<SimuFundCheckOrderPo> orderList = list.getResult();
                alreadyCollectSize = (alreadyCollectSize < 0) ? orderList.size() : (alreadyCollectSize + orderList.size());
                if (CollectionUtils.isEmpty(orderList)) {
                    continue;
                }
                logger.info("taCode:{},taCode:{},totalSize:{},alreadyCollectSize:{},orderListSize:{}", taCode, tradeDt, totalSize, alreadyCollectSize, orderList.size());
                // 插入监控表
                List<EsProOrderMonitorPo> esProOrderMonitors = insertEsProOrder(orderList);
                // 发送kafka
                sendKafka(esProOrderMonitors);
            }
        } catch (Exception e) {
            logger.info("SendEsOrderTaAckDayEndEventListener-sendBatchAckKafka,推送es整个方法出现异常了,error:{}", e.getMessage());
            logger.error("AckDayEndProcessService|sendBatchAckKafka|Exception.errMsg:{}", e.getMessage(), e);
        }
    }

    /**
     * insertEsProOrder:插入监控表
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2019年2月27日 下午3:21:03
     */
    private List<EsProOrderMonitorPo> insertEsProOrder(List<SimuFundCheckOrderPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<EsProOrderMonitorPo> esProOrderMonitors = new ArrayList<>();
        for (SimuFundCheckOrderPo po : list) {
            if (!BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode().equals(po.getmBusiCode())
                    && !BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode().equals(po.getmBusiCode())) {
                String sysCode = ProductTypeEsSysCodeMappingEnum.getSysCode(ProductClassEnum.HIGH.getCode(), po.getFundType());
                EsProOrderMonitorPo monitor = new EsProOrderMonitorPo();
                monitor.setDealNo(po.getDealNo());
                monitor.setSysCode(sysCode);
                monitor.setTxAcctNo(po.getTxAcctNo());
                esProOrderMonitors.add(monitor);
            }
        }
        logger.info("实际{}条，过滤份额迁移后还剩{}条", list.size(), esProOrderMonitors.size());
        logger.info("插入前数据{}", JSON.toJSONString(list));
        logger.info("插入数据{}", JSON.toJSONString(esProOrderMonitors));
        int num = esProOrderMonitorRepository.batchInsert(esProOrderMonitors);
        logger.info("插入监控表{}条", num);
        return esProOrderMonitors;
    }


    private void sendKafka(List<EsProOrderMonitorPo> list) {
        if (CollectionUtils.isEmpty(list)) {
            logger.info("需要发送的数据为空");
            return;
        }
        List<MessageBody> messageBodys = new ArrayList<>();
        MessageBody messageBody = null;
        for (EsProOrderMonitorPo po : list) {
            messageBody = new MessageBody();
            BeanUtils.copyProperties(po, messageBody);
            messageBodys.add(messageBody);
        }

        logger.info("发送es数据{}", JSON.toJSONString(messageBodys));
        msgNotifySendService.sendBatchKafka(messageBodys);
    }

}

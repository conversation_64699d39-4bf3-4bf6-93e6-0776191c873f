package com.howbuy.tms.high.batch.service.business.defaulthighprotocol;

import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.constant.OutReturnCodes;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.fbsonline.protocolbaltransfer.ProtocolBalTransferContext;
import com.howbuy.tms.common.outerservice.fbsonline.protocolbaltransfer.ProtocolBalTransferOuterService;
import com.howbuy.tms.common.outerservice.fbsonline.protocolbaltransfer.ProtocolBalTransferResult;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.querydefaultprotocolbal.QueryDefaultProtocolBalOuterService;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.querydefaultprotocolbal.QueryDefaultProtocolBalResult.FundBalDetail;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.DefaultProtocolTransferRecPo;
import com.howbuy.tms.high.batch.dao.po.order.CustProtocolPo;
import com.howbuy.tms.high.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.event.HighEventPublisher;
import com.howbuy.tms.high.batch.service.event.defaultHighProtocolBal.DefaultHighProtocolBalEvent;
import com.howbuy.tms.high.batch.service.logic.OwnershipTransferOrderLogicService;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.DefaultProtocolTransferRecRepository;
import com.howbuy.tms.high.batch.service.repository.CustProtocolRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @api {MQ} synch_default_high_protocol_bal_queue
 * @apiGroup schedule
 * @apiName 同步高端默认协议号持仓份额
 * @apiDescription 同步高端默认协议号持仓份额
 */
@Service("synchDefaultHighProtocolBalProcessor")
public class SynchDefaultHighProtocolBalProcessor extends BatchMessageProcessor {
    private static Logger logger = LogManager.getLogger(SynchDefaultHighProtocolBalProcessor.class);


    /**
     * 默认协议号业务码
     */
    public static final String DEFAULT_PROTOCOL_BUSI_CODE = "066";
    /**
     * 默认协议号报警时间，每天凌晨22点
     */
    public final static String DEFAULT_PROTOCOL_MONITOR_TM = "235959";

    @Value("${synch.default.high.protocol.bal.queue}")
    private String synchDefaultHighProtocolBalQueue;

    @Autowired
    private LockService lockService;

    @Autowired
    private BusinessBatchFlowRepository businessBatchFlowRepository;

    @Autowired
    private WorkdayService workdayService;

    @Autowired
    private CustProtocolRepository custProtocolRepository;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private QueryDefaultProtocolBalOuterService queryDefaultProtocolBalOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;

    @Autowired
    private ProtocolBalTransferOuterService protocolBalTransferOuterService;

    @Autowired
    private DefaultProtocolTransferRecRepository defaultProtocolTransferRecRepository;

    @Autowired
    private SynchDefaultHighProtocolBalService synchDefaultHighProtocolBalService;

    @Autowired
    private OwnershipTransferOrderLogicService ownershipTransferOrderLogicService;

    @Autowired
    private HighEventPublisher highEventPublisher;

    @Override
    public void doProcessMessage(SimpleMessage message) {
        String uniqKey = CacheKeyPrefix.IDEMPOTENT_KEY_LOCK_PREFIX + synchDefaultHighProtocolBalQueue;
        // 运行锁，只有在获取了锁之后才准许执行
        boolean lock = lockService.getLock(uniqKey, 300);
        if (!lock) {
            logger.info("SynchDefaultHighProtocolBalProcessor|doProcessMessage|get lock fail.");
            return;
        }
        try {
            // 非工作日时间不做同步
            String taTradeDt = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE).getWorkday();
            Date now = new Date();
            String nowDate = DateUtils.formatToString(now, DateUtils.YYYYMMDD);
            if (!taTradeDt.equals(nowDate)) {
                return;
            }
            // 判断日终处理节点是否完成，未完成则不做任何处理
            BusinessBatchFlowPo flowPo = businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(BusinessProcessingStepEnum.BPS_DAY_END_PROCESS.getCode(), taTradeDt, SysCodeEnum.BATCH_HIGH.getCode());
            if (flowPo == null || !BatchStatEnum.PROCESS_SUCCESS.getKey().equals(flowPo.getBatchStat())) {
                return;
            }
            // 默认协议份额迁移
            transferDefaultProtocolBal(now, taTradeDt);
            logger.info("synchDefaultHighProtocolBalProcessor|doProcessMessage|end.");
        } catch (Exception e) {
            String msg = "默认协议处理异常";
            OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);
            logger.error("SynchDefaultHighProtocolBalProcessor|doProcessMessage|error:{}", e.getMessage(), e);
        } finally {
            lockService.releaseLock(uniqKey);
        }
    }

    /**
     * transferDefaultProtocolBal:迁移默认协议份额
     */
    private void transferDefaultProtocolBal(Date now, String taTradeDt) {
        // 查询默认协议的持仓
        List<FundBalDetail> list = queryDefaultProtocolBalOuterService.queryHighDefaultProtocolBal().getFundBalDetailList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> taCodes = new HashSet<>();
        for (FundBalDetail detail : list) {
            // 获取默认协议号,如果返回默认协议号为空，表示获取或者创建协议号失败，则跳过，不做任何处理
            String protocolNo = getCustProtocolNo(detail);
            if (StringUtils.isEmpty(protocolNo)) {
                continue;
            }
            // 构建默认协议号迁移记录
            DefaultProtocolTransferRecPo record = buildTransferRecPo(detail, protocolNo, taTradeDt, now);
            defaultProtocolTransferRecRepository.insert(record);

            // 发起份额迁移
            ProtocolBalTransferContext context = synchDefaultHighProtocolBalService.buildTransferContext(record);

            ProtocolBalTransferResult result = protocolBalTransferOuterService.transfer(context);

            // 处理份额迁移结果
            processTransferResult(context, result, taTradeDt, now, record, taCodes);
        }
        // 匹配非交易转让
        if (CollectionUtils.isNotEmpty(taCodes)) {
            for (String taCode : taCodes) {
                ownershipTransferOrderLogicService.matchNoTradeTransferOrder(taCode, taTradeDt);
            }
        }
        // 监控默认协议份额迁移结果
        synchDefaultHighProtocolBalService.monitorTransferResult(now);
        // 份额迁移事件
        highEventPublisher.publishEvent(new DefaultHighProtocolBalEvent(taTradeDt));
    }


    /**
     * getCustProtocolNo:获取默认协议号
     */
    private String getCustProtocolNo(FundBalDetail detail) {
        String txAcctNo = detail.getCustNo();
        CustProtocolPo custProtocolPo = custProtocolRepository.getCustProtocol(txAcctNo, ProtocolTypeEnum.HIGH_FUND.getCode(), detail.getDisCode(),
                MDataDic.DEFAULT_PRODUCT_CODE);
        if (custProtocolPo == null) {
            custProtocolPo = buildCustProtocolPo(detail);
            custProtocolRepository.saveRecord(custProtocolPo);
        }
        return custProtocolPo.getProtocolNo();
    }

    /**
     * buildTransferRecPo:构建默认协议号持仓记录
     *
     * @param detail
     * @param tProtocolNo
     * @param taTradeDt
     * @param now
     */
    private DefaultProtocolTransferRecPo buildTransferRecPo(FundBalDetail detail, String tProtocolNo, String taTradeDt, Date now) {
        DefaultProtocolTransferRecPo po = new DefaultProtocolTransferRecPo();
        String txAcctNo = detail.getCustNo();
        String cpAcctNo = detail.getCustBankId();
        String disCode = detail.getDisCode();

        po.setRecordNo(sequenceService.getSubmitDealNo(txAcctNo));
        po.setTxAcctNo(txAcctNo);
        po.setDisCode(detail.getDisCode());
        po.setTxChannel(TxChannelEnum.WEBSITE.getCode());
        po.setCpAcctNo(cpAcctNo);
        // 获取客户信息
        QueryAllCustInfoResult result = getCustInfo(txAcctNo, cpAcctNo, disCode);
        if (result != null) {
            CustInfoBean custInfo = result.getCustInfo();
            po.setInvstType(custInfo.getInvstType());
            po.setCustName(custInfo.getCustName());
            po.setIdType(custInfo.getIdType());
            po.setIdNo(custInfo.getIdNo());
        }
        String fundCode = detail.getFundCode();
        // 获取基金信息
        HighProductBaseInfoBean highFundInfo = queryHighProductOuterService.getHighProductBaseInfo(fundCode);
        if (highFundInfo != null) {
            po.setFundName(highFundInfo.getFundName());
            po.setFundShareClass(highFundInfo.getShareClass());
            po.setTaCode(highFundInfo.getTaCode());
        }
        po.setFundCode(detail.getFundCode());
        po.setFundType(detail.getFundType());
        po.setTransferVol(detail.getBalanceVol());
        po.setTaTradeDt(taTradeDt);
        po.setBusiCode(DEFAULT_PROTOCOL_BUSI_CODE);
        po.setTransferStatus(TransferStatusEnum.INIT.getCode());
        po.setProtocolNo(detail.getProtocolNo());
        po.settProtocolNo(tProtocolNo);
        po.setAppDate(DateUtils.formatToString(now, DateUtils.YYYYMMDD));
        po.setAppTime(DateUtils.formatToString(now, DateUtils.HHMMSS));
        po.setCreateDtm(now);
        po.setUpdateDtm(now);
        return po;
    }

    /**
     * buildCustProtocolPo:构建中台默认协议号
     *
     * @param detail
     * @return
     */
    private CustProtocolPo buildCustProtocolPo(FundBalDetail detail) {
        CustProtocolPo po = new CustProtocolPo();
        po.setSubTypeCode(String.valueOf(1L));
        String protocolNo = sequenceService.getProtocolNo(detail.getCustNo());
        po.setProtocolNo(protocolNo);// 协议号
        po.setTxAcctNo(detail.getCustNo());// 交易账号
        po.setDisCode(detail.getDisCode());// 分销机构
        po.setProtocolName("");// 协议号名称
        po.setProtocolType(ProtocolTypeEnum.HIGH_FUND.getCode());// 协议号类型
        po.setProductCode(MDataDic.DEFAULT_PRODUCT_CODE);// 产品代码
        po.setChangeVersion(1); // 产品变更版本
        Date now = new Date();
        po.setCreateDtm(now);// 创建时间
        po.setUpdateDtm(now);// 更新时间
        po.setOpenFlag(SwitchEnum.OPEN.getCode());// 协议开关
        return po;
    }

    /**
     * getCustInfo:获取客户信息
     *
     * @param txAcctNo
     * @param cpAcctNo
     */
    private QueryAllCustInfoResult getCustInfo(String txAcctNo, String cpAcctNo, String disCode) {
        // 调用账户中心查询客户交易账户信息和资金账户信息
        QueryAllCustInfoContext custInfoCtx = new QueryAllCustInfoContext();
        custInfoCtx.setTxAcctNo(txAcctNo);
        custInfoCtx.setCpAcctNo(cpAcctNo);
        custInfoCtx.setDisCode(disCode);
        return queryAllCustInfoOuterService.queryCustInfo(custInfoCtx);
    }

    /**
     * processTransferResult:处理份额迁移结果
     *
     * @param context
     * @param result
     * @param taTradeDt
     * @param now
     * @param record
     * <AUTHOR>
     * @date 2019年8月15日 上午10:46:30
     */
    private void processTransferResult(ProtocolBalTransferContext context, ProtocolBalTransferResult result,
                                       String taTradeDt, Date now,
                                       DefaultProtocolTransferRecPo record, Set<String> taCodes) {
        String returnCode = result.getReturnCode();
        // 成功
        if (StringUtils.isNotEmpty(returnCode) && returnCode.equals(OutReturnCodes.DUBBO_SUCCESS)) {
            // 更新默认协议号迁移记录
            synchDefaultHighProtocolBalService.updateTransferRecPo(result, now, TransferStatusEnum.SUCCESS.getCode());
            // 处理默认协议份额迁移订单
            synchDefaultHighProtocolBalService.processTransferDealOrder(context, result, taTradeDt, now, record);
            // 记录taCode
            if (!StringUtils.isBlank(context.getTaCode())) {
                taCodes.add(context.getTaCode());
            }

        } else {
            synchDefaultHighProtocolBalService.updateTransferRecPo(result, now, TransferStatusEnum.FAIL.getCode());
        }
    }


    @Override
    protected String getQuartMessageChannel() {
        return synchDefaultHighProtocolBalQueue;
    }
}

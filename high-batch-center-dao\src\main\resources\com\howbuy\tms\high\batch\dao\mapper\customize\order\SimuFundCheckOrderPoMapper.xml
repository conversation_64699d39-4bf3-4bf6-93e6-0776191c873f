<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.order.SimuFundCheckOrderPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo"
               extends="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.BaseResultMap">
    </resultMap>

    <!-- 交易未对账交易 -->
    <select id="countDealNotCompDeal" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.TX_COMP_FLAG = '1'<!-- 交易对账标记-1-未对账 -->
        AND T.SUBMIT_APP_FLAG = '2'<!-- 上报状态-2-上报完成 -->
        AND T.TRADE_DT = #{taTradeDt,jdbcType=VARCHAR}
        AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 交易未对账交易 -->
    <select id="queryDealNotCompDeal" resultType="java.lang.String">
        SELECT T.TA_CODE
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.TX_COMP_FLAG = '1'<!-- 交易对账标记-1-未对账 -->
        AND T.SUBMIT_APP_FLAG = '2'<!-- 上报状态-2-上报完成 -->
        AND T.TRADE_DT = #{taTradeDt,jdbcType=VARCHAR}
        AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <!-- 交易对账异常未处理交易 -->
    <select id="countExCompDeal" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.TX_COMP_FLAG = '3'<!-- 交易对账标记-3-金额不一致 -->
        AND T.TRADE_DT = #{taTradeDt,jdbcType=VARCHAR}
        AND T.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
    </select>

    <!-- 自定义代码开始 -->
    <select id="selectCheckOrdersToSubmit" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        from SIMU_FUND_CHECK_ORDER
        where SUBMIT_APP_FLAG in ('1', '3')
          AND PRODUCT_CHANNEL = '3'
          AND TRADE_DT &lt;= #{tradeDt, jdbcType = VARCHAR}
          AND UPDATE_DTM &lt;= #{now,jdbcType=TIMESTAMP} - (1 / 2880)
        <if test="submitRetrieveCtr != null and submitRetrieveCtr == '1'.toString()">
            AND (RETRIEVE_DTM IS NULL OR RETRIEVE_DTM &lt;= #{now,jdbcType=TIMESTAMP})
        </if>
        order by TX_ACCT_NO, FUND_CODE, APP_DTM
    </select>

    <resultMap id="ManualSubmitCheckOrderResultMap"
               type="com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo" extends="BaseResultMap">
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
        <result column="REFUND_DT" jdbcType="VARCHAR" property="refundDt"/>
    </resultMap>
    <select id="selectCheckOrdersToManualSubmit" resultMap="ManualSubmitCheckOrderResultMap">
        select c.*, d.MERGE_SUBMIT_FLAG, d.MAIN_DEAL_ORDER_NO, d.REFUND_DT
        from SIMU_FUND_CHECK_ORDER c
                 left join high_deal_order_dtl d
                           on d.deal_no = c.deal_no
        where c.SUBMIT_APP_FLAG in ('1', '3')
          AND c.TRADE_DT = #{tradeDt, jdbcType = VARCHAR}
          AND c.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
          AND c.TA_CODE = #{taCode,jdbcType=VARCHAR}
          and (d.MERGE_SUBMIT_FLAG is null or d.MERGE_SUBMIT_FLAG != '1' or d.DEAL_NO = d.MAIN_DEAL_ORDER_NO)
        order by d.TX_ACCT_NO, d.FUND_CODE, c.APP_DTM
    </select>


    <select id="selectCheckOrdersToManualSubmitByDealDtlNo" resultMap="ManualSubmitCheckOrderResultMap">
        select c.*, d.MERGE_SUBMIT_FLAG, d.MAIN_DEAL_ORDER_NO, d.REFUND_DT
        from high_deal_order_dtl d
                 left join SIMU_FUND_CHECK_ORDER c
                           on d.deal_no = c.deal_no
        where c.SUBMIT_APP_FLAG in ('1', '3')
          AND d.DEAL_DTL_NO in
        <foreach collection="dealDtlNoList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and (d.MERGE_SUBMIT_FLAG is null or d.MERGE_SUBMIT_FLAG != '1' or d.DEAL_NO = d.MAIN_DEAL_ORDER_NO)
        order by d.TX_ACCT_NO, d.FUND_CODE, c.APP_DTM
    </select>

    <select id="selectCheckOrdersToManualSubmitByMainDealNo" resultMap="ManualSubmitCheckOrderResultMap">
        select c.*, d.MERGE_SUBMIT_FLAG, d.MAIN_DEAL_ORDER_NO, d.REFUND_DT
        from SIMU_FUND_CHECK_ORDER c
                 inner join high_deal_order_dtl d
                            on d.deal_no = c.deal_no
        where d.MAIN_DEAL_ORDER_NO = #{mainDealNo, jdbcType = VARCHAR}
        <!-- 认申购合并上报需保证订单号正序，避免拆分费用时中后台不一致 -->
        order by d.deal_no
    </select>

    <!-- 重新上报，设置各标记位 -->
    <update id="updateReSubmittingByDealDtlNo"
            parameterType="com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo">
        <!-- 1、上次上报完成了，才允许更新为“上报中” 2、更新交易申请标记；3、更新交易对账标记=未对账 -->
        update SIMU_FUND_CHECK_ORDER
        set SUBMIT_APP_FLAG = #{submitAppFlag,jdbcType=CHAR},
            TX_APP_FLAG     = #{txAppFlag,jdbcType=CHAR},
            TX_COMP_FLAG    = '1',
            UPDATE_DTM      = #{updateDtm, jdbcType=TIMESTAMP}
        where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
          AND SUBMIT_APP_FLAG = '2'
    </update>

    <update id="updateNotNeedSubmitByDealDtlNo"
            parameterType="com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo">
        <!-- 1、“上报中” 更新为 "无需上报" 2、更新交易申请标记；3、更新交易对账标记=无需对账 -->
        update SIMU_FUND_CHECK_ORDER
        set SUBMIT_APP_FLAG = #{submitAppFlag,jdbcType=CHAR},
            TX_APP_FLAG     = #{txAppFlag,jdbcType=CHAR},
            TX_COMP_FLAG    = '0',
            UPDATE_DTM      = #{updateDtm, jdbcType=TIMESTAMP}
        where DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
          AND SUBMIT_APP_FLAG = '1'
    </update>

    <!-- 查询交易上报完成对账状态为未对账、最后更新时间再3分钟之前的交易对账订单信息， 如果是申购类订单需要支付对账标记为对账完成 -->
    <select id="selectUnCheckOrderWithPage" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM (
        SELECT t.*, p.pmt_comp_flag FROM (
        SELECT *
        FROM simu_fund_check_order
        WHERE submit_app_flag = '2'
          AND tx_comp_flag = '1'
          and DEAL_TYPE = '2'
        <!-- 如果没有传指定日期，基于性能考虑默认查询当前时间到30天的所有未对账记录 -->
        <choose>
            <when test="tradeDt != null">
                AND trade_dt = #{tradeDt,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND create_dtm &gt;= #{startDtm,jdbcType=TIMESTAMP}
            </otherwise>
        </choose>
        <if test="endDtm != null">
            AND create_dtm &lt;= #{endDtm,jdbcType=TIMESTAMP}
        </if>
        AND update_dtm &lt;= #{lastUpdateDtm, jdbcType=TIMESTAMP}
        ) t
            LEFT JOIN payment_order p
                      ON t.deal_no = p.deal_no
        ) t
        WHERE (pmt_comp_flag = '2' or pmt_comp_flag is null)
        ORDER BY create_dtm
    </select>

    <!-- 根据交易明细订单号查询私募对账订单信息 -->
    <select id="selectByDealDtlNo" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM simu_fund_check_order
        WHERE DEAL_DTL_NO = #{dealDtlNo,jdbcType=VARCHAR}
    </select>

    <!-- 根据后台交易订单号查询私募对账订单信息 -->
    <select id="getByContractNo" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM simu_fund_check_order
        WHERE contract_no = #{contractNo,jdbcType=VARCHAR}
    </select>

    <resultMap id="VolConfirmBookResultMap" type="com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto"
               extends="BaseResultMap">
        <result column="merge_submit_flag" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="main_deal_order_no" jdbcType="VARCHAR" property="mainDealOrderNo"/>
    </resultMap>
    <select id="selectNeedGenVolConfirmBookByAckImportDt" resultMap="VolConfirmBookResultMap" parameterType="map">
        SELECT c.*, d.merge_submit_flag, d.main_deal_order_no
        FROM simu_fund_check_order c
                 left join high_deal_order_dtl d
                           on c.deal_no = d.deal_no
        WHERE c.ACK_IMPORT_DT = #{ackImportDt,jdbcType=VARCHAR}
          AND c.M_BUSI_CODE IN ('1120', '1122', '1124')
          AND c.TX_RATIO IN ('1', '3')
          AND c.TX_STATUS = '3'
          and (d.main_deal_order_no is null or d.main_deal_order_no = d.deal_no)
    </select>
    <select id="selectTotalAmountByMainDealNo" parameterType="map" resultMap="BaseResultMap">
        select sum(c.ack_vol)         ack_vol,
               sum(c.ack_amt)         ack_amt,
               sum(c.fee)             fee,
               sum(c.vol_by_interest) vol_by_interest
        from simu_fund_check_order c
                 left join high_deal_order_dtl d
                           on c.deal_no = d.deal_no
        where d.main_deal_order_no = #{mainDealNo,jdbcType=VARCHAR}
    </select>

    <!-- 根据上报申请订单号更新对账订单中的交易申请标记或者交易对账标记 -->
    <update id="updateAppFlagOrCompFlagBySubmitDealNo" parameterType="map">
        update SIMU_FUND_CHECK_ORDER
        set
        <if test="txAppFlag != null">
            TX_APP_FLAG = #{txAppFlag,jdbcType=CHAR},
        </if>
        <if test="txCompFlag != null">
            TX_COMP_FLAG = #{txCompFlag,jdbcType=CHAR},
        </if>
        <if test="submitAppFlag != null">
            SUBMIT_APP_FLAG = #{submitAppFlag,jdbcType=CHAR},
        </if>
        <if test="cancelOrderSrc != null">
            CANCEL_ORDER_SRC = #{cancelOrderSrc,jdbcType=CHAR},
        </if>
        UPDATE_DTM = #{now,jdbcType=TIMESTAMP}
        where SUBMIT_DEAL_NO = #{submitDealNo,jdbcType=VARCHAR}
          and UPDATE_DTM = #{oldUpdateDtm,jdbcType=TIMESTAMP}
    </update>

    <select id="selectSimuFundCheckOrderWithPage" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM SIMU_FUND_CHECK_ORDER
        <where>
            <if test="condition.tradeDt != null">
                AND TRADE_DT = #{condition.tradeDt,jdbcType=VARCHAR}
            </if>
            <if test="condition.taCode != null">
                AND TA_CODE = #{condition.taCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by APP_DATE desc, APP_TIME desc, SUBMIT_DEAL_NO desc
    </select>

    <resultMap id="ConsoleResultMap" type="com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto"
               extends="BaseResultMap">
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
    </resultMap>
    <!-- 中控台-交易申请查询 -->
    <select id="selectSimuFundCheckOrderForConsole" parameterType="map" resultMap="ConsoleResultMap">
        SELECT t1.*, t2.MERGE_SUBMIT_FLAG, t2.MAIN_DEAL_ORDER_NO
        FROM SIMU_FUND_CHECK_ORDER t1
                 left join high_deal_order_dtl t2
                           on t1.deal_no = t2.deal_no
        <where>
            <if test="condition.txAcctNo != null">
                t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.custName != null">
                AND t1.CUST_NAME = #{condition.custName,jdbcType=VARCHAR}
            </if>
            <if test="condition.idNo != null">
                AND t1.ID_NO = #{condition.idNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.bankAcct != null">
                AND t1.BANK_ACCT = #{condition.bankAcct,jdbcType=VARCHAR}
            </if>
            <if test="condition.mBusiCode != null">
                AND t1.M_BUSI_CODE = #{condition.mBusiCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.dealNo != null">
                AND t1.DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.dealDtlNo != null">
                AND t1.DEAL_DTL_NO = #{condition.dealDtlNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.submitDealNo != null">
                AND t1.SUBMIT_DEAL_NO = #{condition.submitDealNo,jdbcType=VARCHAR}
            </if>
            <if test="appDateStart != null">
                AND t1.APP_DATE <![CDATA[ >= ]]> #{appDateStart,jdbcType=VARCHAR}
            </if>
            <if test="appDateEnd != null">
                AND t1.APP_DATE <![CDATA[ <= ]]> #{appDateEnd,jdbcType=VARCHAR}
            </if>
            <if test="taTradeDtStart != null">
                AND t1.TA_TRADE_DT <![CDATA[ >= ]]> #{taTradeDtStart,jdbcType=VARCHAR}
            </if>
            <if test="taTradeDtEnd != null">
                AND t1.TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDtEnd,jdbcType=VARCHAR}
            </if>
            <if test="condition.ackDt != null">
                AND t1.ACK_DT = #{condition.ackDt,jdbcType=VARCHAR}
            </if>
            <if test="condition.ackImportDt != null">
                AND t1.ACK_IMPORT_DT = #{condition.ackImportDt,jdbcType=VARCHAR}
            </if>
            <if test="condition.fundCode != null">
                AND t1.FUND_CODE = #{condition.fundCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.txAppFlag != null">
                AND t1.TX_APP_FLAG = #{condition.txAppFlag,jdbcType=VARCHAR}
            </if>
            <if test="condition.txCompFlag != null">
                AND t1.TX_COMP_FLAG = #{condition.txCompFlag,jdbcType=VARCHAR}
            </if>
            <if test="condition.submitAppFlag != null">
                AND t1.SUBMIT_APP_FLAG = #{condition.submitAppFlag,jdbcType=VARCHAR}
            </if>
            <if test="condition.txStatus != null">
                AND t1.TX_STATUS = #{condition.txStatus,jdbcType=VARCHAR}
            </if>
            <if test="condition.txRatio != null">
                AND t1.TX_RATIO = #{condition.txRatio,jdbcType=VARCHAR}
            </if>
            <if test="condition.disCode != null">
                AND t1.DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.txChannel != null">
                AND t1.TX_CHANNEL = #{condition.txChannel,jdbcType=VARCHAR}
            </if>
            <if test="condition.tradeDt != null">
                AND t1.TRADE_DT = #{condition.tradeDt,jdbcType=VARCHAR}
            </if>
            <if test="condition.submitTaDt != null">
                AND t1.SUBMIT_TA_DT = #{condition.submitTaDt,jdbcType=VARCHAR}
            </if>
            <if test="condition.productChannel != null">
                AND t1.PRODUCT_CHANNEL = #{condition.productChannel,jdbcType=VARCHAR}
            </if>
            <if test="condition.taCode != null">
                AND t1.TA_CODE = #{condition.taCode,jdbcType=VARCHAR}
            </if>
            <if test='mergeSubmitFlag == "0"'>
                AND (t2.MERGE_SUBMIT_FLAG != '1' or t2.MERGE_SUBMIT_FLAG is null)
            </if>
            <if test='mergeSubmitFlag == "1"'>
                AND t2.MERGE_SUBMIT_FLAG = '1'
            </if>
            <if test="filterFundCodeList != null and filterFundCodeList.size() > 0">
                and  t1.FUND_CODE not in
                <foreach collection="filterFundCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t1.APP_DATE desc, t1.APP_TIME desc, t1.SUBMIT_DEAL_NO desc
    </select>
    <!-- 中控台-交易申请统计 -->
    <select id="selectCountSimuFundCheckOrderForConsole" parameterType="map" resultMap="BaseResultMap">
        select ifnull(sum(APP_AMT), 0) as APP_AMT, ifnull(sum(APP_VOL), 0) as APP_VOL
        FROM SIMU_FUND_CHECK_ORDER
        <where>
            <if test="condition.txAcctNo != null">
                TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.custName != null">
                AND CUST_NAME = #{condition.custName,jdbcType=VARCHAR}
            </if>
            <if test="condition.idNo != null">
                AND ID_NO = #{condition.idNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.bankAcct != null">
                AND BANK_ACCT = #{condition.bankAcct,jdbcType=VARCHAR}
            </if>
            <if test="condition.mBusiCode != null">
                AND M_BUSI_CODE = #{condition.mBusiCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.dealNo != null">
                AND DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.dealDtlNo != null">
                AND DEAL_DTL_NO = #{condition.dealDtlNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.submitDealNo != null">
                AND SUBMIT_DEAL_NO = #{condition.submitDealNo,jdbcType=VARCHAR}
            </if>
            <if test="appDateStart != null">
                AND APP_DATE <![CDATA[ >= ]]> #{appDateStart,jdbcType=VARCHAR}
            </if>
            <if test="appDateEnd != null">
                AND APP_DATE <![CDATA[ <= ]]> #{appDateEnd,jdbcType=VARCHAR}
            </if>
            <if test="taTradeDtStart != null">
                AND TA_TRADE_DT <![CDATA[ >= ]]> #{taTradeDtStart,jdbcType=VARCHAR}
            </if>
            <if test="taTradeDtEnd != null">
                AND TA_TRADE_DT <![CDATA[ <= ]]> #{taTradeDtEnd,jdbcType=VARCHAR}
            </if>
            <if test="condition.ackDt != null">
                AND ACK_DT = #{condition.ackDt,jdbcType=VARCHAR}
            </if>
            <if test="condition.fundCode != null">
                AND FUND_CODE = #{condition.fundCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.txAppFlag != null">
                AND TX_APP_FLAG = #{condition.txAppFlag,jdbcType=VARCHAR}
            </if>
            <if test="condition.txCompFlag != null">
                AND TX_COMP_FLAG = #{condition.txCompFlag,jdbcType=VARCHAR}
            </if>
            <if test="condition.submitAppFlag != null">
                AND SUBMIT_APP_FLAG = #{condition.submitAppFlag,jdbcType=VARCHAR}
            </if>
            <if test="condition.txStatus != null">
                AND TX_STATUS = #{condition.txStatus,jdbcType=VARCHAR}
            </if>
            <if test="condition.txRatio != null">
                AND TX_RATIO = #{condition.txRatio,jdbcType=VARCHAR}
            </if>
            <if test="condition.disCode != null">
                AND DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.txChannel != null">
                AND TX_CHANNEL = #{condition.txChannel,jdbcType=VARCHAR}
            </if>
            <if test="filterFundCodeList != null and filterFundCodeList.size() > 0">
                and  FUND_CODE not in
                <foreach collection="filterFundCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <resultMap id="DealCheckOrderResultMap" type="com.howbuy.tms.high.batch.dao.vo.DealCheckOrderVo">
        <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo"/>
        <result column="PMT_DEAL_NO" jdbcType="VARCHAR" property="pmtDealNo"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="ID_NO" jdbcType="VARCHAR" property="idNo"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="txPmtFlag"/>
        <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType"/>
        <result column="PMT_COMP_FLAG" jdbcType="VARCHAR" property="pmtCompFlag"/>
        <result column="APP_DATE" jdbcType="VARCHAR" property="appDate"/>
        <result column="APP_TIME" jdbcType="VARCHAR" property="appTime"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="TX_APP_FLAG" jdbcType="VARCHAR" property="txAppFlag"/>
        <result column="TX_RATIO" jdbcType="VARCHAR" property="txRatio"/>
        <result column="TX_STATUS" jdbcType="VARCHAR" property="txStatus"/>
        <result column="TX_COMP_FLAG" jdbcType="VARCHAR" property="txCompFlag"/>
        <result column="INTEREST" jdbcType="DECIMAL" property="interest"/>
        <result column="RECUPERATE_FEE" jdbcType="DECIMAL" property="recuperateFee"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
        <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType"/>
        <result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType"/>
        <result column="T_FUND_CODE" jdbcType="VARCHAR" property="tFundCode"/>
        <result column="T_FUND_NAME" jdbcType="VARCHAR" property="tFundName"/>
        <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode"/>
        <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName"/>
        <result column="TRADE_DT" jdbcType="VARCHAR" property="tradeDt"/>
    </resultMap>
    <!-- 根据交易对账状态查询订单信息 -->
    <select id="selectDealCheckOrderByTaTradeDateAndTxCompFlag" parameterType="map" resultMap="DealCheckOrderResultMap">
        select t1.DEAL_NO,
               t1.DEAL_DTL_NO,
               t3.PMT_DEAL_NO,
               t1.CONTRACT_NO,
               t1.SUBMIT_DEAL_NO,
               t1.TX_ACCT_NO,
               t2.CUST_NAME,
               t1.ID_NO,
               t1.BANK_ACCT,
               t1.M_BUSI_CODE,
               t2.PRODUCT_CODE,
               t1.FUND_CODE,
               t1.APP_AMT,
               t1.APP_VOL,
               t3.TX_PMT_FLAG,
               t2.PAYMENT_TYPE,
               t3.PMT_COMP_FLAG,
               t1.APP_DATE,
               t1.APP_TIME,
               t1.TA_TRADE_DT,
               t1.TX_APP_FLAG,
               t1.TX_RATIO,
               t1.TX_STATUS,
               t1.TX_COMP_FLAG,
               t1.RECUPERATE_FEE,
               t2.PRODUCT_CHANNEL,
               t1.FUND_TYPE,
               t1.FUND_SUB_TYPE,
               t1.T_FUND_CODE,
               t1.T_FUND_NAME,
               t1.FUND_NAME,
               t1.INTEREST,
               t1.ACHIEVEMENT_PAY,
               t1.ACHIEVEMENT_COMPEN,
               t1.VOL_BY_INTEREST,
               t1.TRADE_DT
        from SIMU_FUND_CHECK_ORDER t1
                 left join DEAL_ORDER t2 on t1.deal_no = t2.deal_no
                 left join PAYMENT_ORDER t3 on t1.deal_no = t3.deal_no
        where t2.deal_type = '2'
        <if test="filterFundCodeList != null and filterFundCodeList.size() > 0">
            and  t1.FUND_CODE not in
            <foreach collection="filterFundCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        and t1.trade_dt = #{taTradeDt,jdbcType=VARCHAR}
        and t1.tx_comp_flag = #{txCompFlag,jdbcType=VARCHAR}
    </select>

    <!-- 根据交易对账状态查询订单信息 -->
    <select id="sumAppAmt" parameterType="map" resultType="java.math.BigDecimal">
        select ifnull(sum(t1.app_amt), 0)
        from SIMU_FUND_CHECK_ORDER t1
        where t1.trade_dt = #{taTradeDt,jdbcType=VARCHAR}
          and t1.tx_comp_flag = #{txCompFlag,jdbcType=VARCHAR}
    </select>

    <resultMap id="FundCheckOrderResultMap" type="com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderVo">
        <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="BUSI_CODE" jdbcType="VARCHAR" property="busiCode"/>
        <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
    </resultMap>

    <!-- 查询大于等于指定TA工作日的上报成功的上报订单 -->
    <select id="selectByGtTaTradeDt" parameterType="map" resultMap="FundCheckOrderResultMap">
        select t1.DEAL_NO,
               t1.DEAL_DTL_NO,
               t1.CONTRACT_NO,
               t1.SUBMIT_DEAL_NO,
               t1.TA_TRADE_DT,
               t1.BUSI_CODE,
               t1.SUBMIT_TA_DT
        from SIMU_FUND_CHECK_ORDER t1
        where submit_app_flag in ('2', '3')
          and submit_ta_dt &gt;= #{taTradeDt,jdbcType=VARCHAR}
          and product_channel = '3'
    </select>

    <!-- 更新上报订单TA工作日 -->
    <update id="updateSubmitTaDt" parameterType="map">
        update SIMU_FUND_CHECK_ORDER
        set
        <if test="newSubmitTaDt != null">
            SUBMIT_TA_DT = #{newSubmitTaDt,jdbcType=VARCHAR},
        </if>
        <if test="busiCode != null">
            BUSI_CODE = #{busiCode,jdbcType=CHAR},
        </if>
        <if test="mBusiCode != null">
            M_BUSI_CODE = #{mBusiCode,jdbcType=CHAR},
        </if>
        UPDATE_DTM = NOW()
        where SUBMIT_DEAL_NO = #{submitDealNo,jdbcType=VARCHAR}
          and SUBMIT_TA_DT = #{oldSubmitTaDt,jdbcType=VARCHAR}
    </update>

    <resultMap id="TradeCheckCountResultMap" type="com.howbuy.tms.high.batch.dao.vo.TradeCheckCountVo">
        <result column="TOTAL_NUM" jdbcType="INTEGER" property="totalNum"/>
        <result column="TOTAL_AMT" jdbcType="DECIMAL" property="totalAmt"/>
        <result column="TOTAL_VOL" jdbcType="DECIMAL" property="totalVol"/>
    </resultMap>

    <!-- 统计交易上报成功记录明细 -->
    <select id="countFundCheckByTradeDt" parameterType="map" resultMap="TradeCheckCountResultMap">
        select count(1) as TOTAL_NUM, ifnull(sum(app_amt), 0) as TOTAL_AMT, ifnull(sum(app_vol), 0) as TOTAL_VOL
        from SIMU_FUND_CHECK_ORDER
        where trade_dt = #{tradeDt,jdbcType=VARCHAR}
          and tx_app_flag = '0'
          AND SUBMIT_DEAL_NO NOT LIKE '99%'
          and PRODUCT_CHANNEL = '3'
          AND SUBMIT_APP_FLAG = '2'
    </select>

    <resultMap id="TradeCheckDetailResultMap" type="com.howbuy.tms.high.batch.dao.vo.TradeCheckDetailVo">
        <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
    </resultMap>

    <!-- 查询交易上报成功明细 -->
    <select id="selectFundCheckByTradeDt" parameterType="map" resultMap="TradeCheckDetailResultMap">
        SELECT SUBMIT_DEAL_NO, CONTRACT_NO, CUST_NAME, TX_ACCT_NO, APP_AMT, APP_VOL
        FROM SIMU_FUND_CHECK_ORDER
        WHERE TRADE_DT = #{tradeDt,jdbcType=VARCHAR}
          AND TX_APP_FLAG = '0'
          AND SUBMIT_APP_FLAG = '2'
          AND SUBMIT_DEAL_NO NOT LIKE '99%'
          AND PRODUCT_CHANNEL = '3'
    </select>

    <update id="updateSubmitFlagWithForce" parameterType="map">
        update SIMU_FUND_CHECK_ORDER
        set TX_APP_FLAG      = #{simuFundCheckOrder.txAppFlag,jdbcType=CHAR},
            SUBMIT_APP_FLAG  = #{simuFundCheckOrder.submitAppFlag,jdbcType=CHAR},
            TX_COMP_FLAG     = #{simuFundCheckOrder.txCompFlag,jdbcType=CHAR},
            MEMO             = #{simuFundCheckOrder.memo,jdbcType=VARCHAR},
            UPDATE_DTM       = #{simuFundCheckOrder.updateDtm,jdbcType=TIMESTAMP},
            CANCEL_ORDER_SRC = #{simuFundCheckOrder.cancelOrderSrc,jdbcType=CHAR},
            RET_CODE         = #{simuFundCheckOrder.retCode,jdbcType=VARCHAR},
            RET_DESC         = #{simuFundCheckOrder.retDesc,jdbcType=VARCHAR}
        where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
          AND TX_APP_FLAG = '3'
    </update>

    <resultMap id="ShareMergeOrderResultMap" type="com.howbuy.tms.high.batch.dao.vo.ShareMergeOrderVo">
        <result column="deal_no" jdbcType="VARCHAR" property="dealNo"/>
        <result column="total_size" jdbcType="INTEGER" property="totalSize"/>
    </resultMap>

    <!-- 查询需要上报的份额合并/迁移对账订单信息 -->
    <select id="selectShareMergeCheckOrdersToSubmit" resultMap="ShareMergeOrderResultMap">
        <!-- 查询48小时以内待上报的对账明细 -->
        select DEAL_NO,
               COUNT(DEAL_NO) AS TOTAL_SIZE
        from SIMU_FUND_CHECK_ORDER
    <![CDATA[ 
        where SUBMIT_APP_FLAG in ('1', '3')
          AND M_BUSI_CODE IN ('1365', '1366')
          AND CREATE_DTM >= #{startDtm,jdbcType=TIMESTAMP}
          AND UPDATE_DTM <= DATE_SUB(NOW(), INTERVAL 30 SECOND)
    ]]>
    GROUP BY DEAL_NO
    </select>

    <!-- 查询份额合并/迁移转入上报订单号 -->
    <select id="selectShareMergeInCheckOrder" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM SIMU_FUND_CHECK_ORDER
        WHERE DEAL_NO = #{dealNo,jdbcType=VARCHAR}
          AND BUSI_CODE = '064'
    </select>

    <!-- 根据主订单号和中台业务代码查询交易订单明细列表信息 -->
    <select id="selectShareMergeOutByDealNo" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        from SIMU_FUND_CHECK_ORDER
        where deal_no = #{dealNo,jdbcType=VARCHAR}
          and busi_code = '062'
    </select>

    <!-- 根据主订单号更新上报状态 -->
    <update id="updateByDealNo" parameterType="map">
        update SIMU_FUND_CHECK_ORDER
        set
        <if test="submitAppFlag != null">
            SUBMIT_APP_FLAG = #{submitAppFlag,jdbcType=CHAR},
        </if>
        UPDATE_DTM = NOW()
        where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
    </update>

    <!-- 根据订单号查询份额合并/迁移转出上报订单 -->
    <select id="selectShareMergeCheckOrdersToRefershResult" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM SIMU_FUND_CHECK_ORDER
        WHERE BUSI_CODE IN ('062', '064')
          AND SUBMIT_APP_FLAG = '2'
          AND TX_COMP_FLAG = '2'
          AND TX_STATUS = '0'
          AND CREATE_DTM &gt;= #{startDtm,jdbcType=TIMESTAMP}
    </select>

    <update id="updateRetrieveDtm" parameterType="map">
        UPDATE SIMU_FUND_CHECK_ORDER T
        SET t.RETRIEVE_DTM = #{retrieveDtm,jdbcType=TIMESTAMP}
        WHERE t.SUBMIT_DEAL_NO = #{submitDealNo,jdbcType=VARCHAR}
    </update>
    <!-- 查询交易上报需要修改赎回方向的数据-->
    <select id="selectFundCheckByDisDealAckNo" parameterType="map" resultMap="BaseResultMap">
        SELECT DEAL_NO, DIS_DEAL_ACK_NO, REDEEM_DIRECTION
        FROM SIMU_FUND_CHECK_ORDER
        WHERE
            DIS_DEAL_ACK_NO IN
        <foreach collection="disDealAckNoList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateRedeemDirection" parameterType="map">
        update SIMU_FUND_CHECK_ORDER
        set REDEEM_DIRECTION = #{redeemDirection,jdbcType=CHAR},
            UPDATE_DTM       = NOW()
        where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
    </update>

    <!-- 上报中或重新上报订单 -->
    <select id="countSubmitingOrReSubmit" parameterType="map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.SUBMIT_APP_FLAG in ('1', '3')
          AND T.TRADE_DT = #{tradeDt,jdbcType=VARCHAR}
          AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <!-- 上报中或重新上报订单 -->
    <select id="querySubmitingOrReSubmit" parameterType="map" resultType="java.lang.String">
        SELECT T.TA_CODE
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.SUBMIT_APP_FLAG in ('1', '3')
          AND T.TRADE_DT = #{tradeDt,jdbcType=VARCHAR}
          AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 上报中或重新上报订单 -->
    <select id="querySubmitingOrReSubmitByDt" parameterType="map" resultType="java.lang.String">
        SELECT T.TA_CODE
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.SUBMIT_APP_FLAG in ('1', '3')
          AND T.TRADE_DT <![CDATA[ >= ]]> #{signStartDt,jdbcType=VARCHAR}
          AND T.TRADE_DT <![CDATA[ <= ]]> #{endTradeDt,jdbcType=VARCHAR}
          AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectSubmitingOrReSubmit" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.SUBMIT_APP_FLAG in ('1', '3')
          AND T.TRADE_DT = #{tradeDt,jdbcType=VARCHAR}
          AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectSubmitingOrReSubmitByDt" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.SUBMIT_APP_FLAG in ('1', '3')
          AND T.TRADE_DT <![CDATA[ >= ]]> #{signStartDt,jdbcType=VARCHAR}
          AND T.TRADE_DT <![CDATA[ <= ]]> #{endTradeDt,jdbcType=VARCHAR}
          AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectListDealNotCheck" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.TX_COMP_FLAG = '1'<!-- 交易对账标记-1-未对账 -->
        AND T.SUBMIT_APP_FLAG = '2'<!-- 上报状态-2-上报完成 -->
        AND T.TRADE_DT = #{taTradeDt,jdbcType=VARCHAR}
        AND T.TA_CODE IN
        <foreach collection="taCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateSubmitTaDtByDealNo" parameterType="map">
        update SIMU_FUND_CHECK_ORDER
        set
        <if test="newSubmitTaDt != null">
            SUBMIT_TA_DT = #{newSubmitTaDt,jdbcType=VARCHAR},
            TRADE_DT     = #{newSubmitTaDt,jdbcType=VARCHAR},
        </if>
        UPDATE_DTM = #{updateDtm,jdbcType=VARCHAR}
        where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
        <if test="oldSubmitTaDt != null">
            and SUBMIT_TA_DT = #{oldSubmitTaDt,jdbcType=VARCHAR}
        </if>
    </update>

    <!-- 高端确认数据Map -->
    <resultMap id="highTradeAckData" type="com.howbuy.tms.high.batch.dao.vo.HighTradeAckDataVo">
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode"/>
        <result column="BUSI_CODE" jdbcType="CHAR" property="busiCode"/>
        <result column="APP_DT" jdbcType="VARCHAR" property="appDt"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt"/>
        <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol"/>
        <result column="NAV" jdbcType="DECIMAL" property="nav"/>
        <result column="FEE" jdbcType="DECIMAL" property="fee"/>
        <result column="TX_RATIO" jdbcType="CHAR" property="txRatio"/>
        <result column="TX_STATUS" jdbcType="CHAR" property="txStatus"/>
        <result column="MEMO" jdbcType="VARCHAR" property="memo"/>
        <result column="TA_CODE" jdbcType="VARCHAR" property="taCode"/>
        <result column="PROTOCOL_NO" jdbcType="VARCHAR" property="protocolNo"/>
        <result column="REDEEM_DIRECTION" jdbcType="VARCHAR" property="redeemDirection"/>
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
    </resultMap>
    <select id="selectHighTradeAckDataForConsole" parameterType="map" resultMap="highTradeAckData">
        SELECT T.CONTRACT_NO,
               T.SUBMIT_DEAL_NO,
               T.TX_ACCT_NO,
               T.DIS_CODE,
               T.CP_ACCT_NO,
               T.FUND_CODE,
               T.FUND_SHARE_CLASS,
               T.BUSI_CODE,
               T.APP_DATE AS        APP_DT,
               ifnull(T.APP_AMT, 0) APP_AMT,
               ifnull(T.APP_VOL, 0) APP_VOL,
               T.ACK_DT,
               ifnull(T.ACK_AMT, 0) ACK_AMT,
               ifnull(T.ACK_VOL, 0) ACK_VOL,
               ifnull(T.NAV, 0)     NAV,
               ifnull(T.FEE, 0)     FEE,
               T.TX_RATIO,
               T.TX_STATUS,
               T.MEMO,
               T.TA_CODE,
               T.PROTOCOL_NO,
               T.REDEEM_DIRECTION,
               t1.MERGE_SUBMIT_FLAG,
               t1.MAIN_DEAL_ORDER_NO
        FROM SIMU_FUND_CHECK_ORDER T
                 left join HIGH_DEAL_ORDER_DTL T1
                           ON T.DEAL_NO = T1.DEAL_NO
        <where>
            <if test="condition.txAcctNo != null">
                T.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.busiCode != null">
                AND T.BUSI_CODE = #{condition.busiCode,jdbcType=CHAR}
            </if>
            <if test="condition.submitDealNo != null">
                AND T.SUBMIT_DEAL_NO = #{condition.submitDealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.contractNo != null">
                AND T.CONTRACT_NO = #{condition.contractNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.fundCode != null">
                AND T.FUND_CODE = #{condition.fundCode,jdbcType=VARCHAR}
            </if>
            <if test="ackDtStart != null">
                AND T.ACK_DT <![CDATA[ >= ]]> #{ackDtStart,jdbcType=VARCHAR}
            </if>
            <if test="ackDtEnd != null">
                AND T.ACK_DT <![CDATA[ <= ]]> #{ackDtEnd,jdbcType=VARCHAR}
            </if>
            <if test="condition.txRatio != null">
                AND T.TX_RATIO = #{condition.txRatio,jdbcType=CHAR}
            </if>
            <if test="condition.txStatus != null">
                AND T.TX_STATUS = #{condition.txStatus,jdbcType=CHAR}
            </if>
            <if test="condition.disCode != null">
                AND T.DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
            </if>
            <if test='mergeSubmitFlag == "0"'>
                AND (t1.MERGE_SUBMIT_FLAG != '1' or t1.MERGE_SUBMIT_FLAG is null)
            </if>
            <if test='mergeSubmitFlag == "1"'>
                AND t1.MERGE_SUBMIT_FLAG = '1'
            </if>
            <if test="filterFundCodeList != null and filterFundCodeList.size() > 0">
                and  T.FUND_CODE not in
                <foreach collection="filterFundCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by T.ACK_DT desc, T.SUBMIT_DEAL_NO desc
    </select>

    <select id="selectCountHighTradeAckDataForConsole" parameterType="map" resultMap="highTradeAckData">
        SELECT SUM(T.ACK_AMT) ACK_AMT,
               SUM(T.ACK_VOL) ACK_VOL
        FROM SIMU_FUND_CHECK_ORDER T
        <where>
            <if test="condition.txAcctNo != null">
                T.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.busiCode != null">
                AND T.BUSI_CODE = #{condition.busiCode,jdbcType=CHAR}
            </if>
            <if test="condition.submitDealNo != null">
                AND T.SUBMIT_DEAL_NO = #{condition.submitDealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.contractNo != null">
                AND T.CONTRACT_NO = #{condition.contractNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.fundCode != null">
                AND T.FUND_CODE = #{condition.fundCode,jdbcType=VARCHAR}
            </if>
            <if test="ackDtStart != null">
                AND T.ACK_DT <![CDATA[ >= ]]> #{ackDtStart,jdbcType=VARCHAR}
            </if>
            <if test="ackDtEnd != null">
                AND T.ACK_DT <![CDATA[ <= ]]> #{ackDtEnd,jdbcType=VARCHAR}
            </if>
            <if test="condition.txRatio != null">
                AND T.TX_RATIO = #{condition.txRatio,jdbcType=CHAR}
            </if>
            <if test="condition.txStatus != null">
                AND T.TX_STATUS = #{condition.txStatus,jdbcType=CHAR}
            </if>
            <if test="condition.disCode != null">
                AND T.DIS_CODE = #{condition.disCode,jdbcType=VARCHAR}
            </if>
            <if test="filterFundCodeList != null and filterFundCodeList.size() > 0">
                and  T.FUND_CODE not in
                <foreach collection="filterFundCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="countSubmit" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM SIMU_FUND_CHECK_ORDER T
        WHERE T.SUBMIT_APP_FLAG = '2'
          AND (T.CANCEL_ORDER_SRC IS NULL OR T.CANCEL_ORDER_SRC='' OR T.CANCEL_ORDER_SRC = '1' OR T.CANCEL_ORDER_SRC = '2')
          AND T.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
          AND T.TRADE_DT = #{tradeDt,jdbcType=VARCHAR}
          AND T.TA_CODE = #{taCode,jdbcType=VARCHAR}
          AND T.M_BUSI_CODE NOT IN ('1366', '1365')
    </select>

    <resultMap id="HighDealSubmitResultMap" type="com.howbuy.tms.high.batch.dao.vo.HighDealSubmitDtlVo">
        <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="ID_NO" jdbcType="VARCHAR" property="idNo"/>
        <result column="ID_TYPE" jdbcType="VARCHAR" property="idType"/>
        <result column="INVST_TYPE" jdbcType="VARCHAR" property="invstType"/>
        <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode"/>
        <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode"/>
        <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="APP_DATE" jdbcType="VARCHAR" property="appDate"/>
        <result column="APP_TIME" jdbcType="VARCHAR" property="appTime"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="CALM_DTM" jdbcType="DECIMAL" property="calmDtm"/>
        <result column="TX_APP_FLAG" jdbcType="VARCHAR" property="txAppFlag"/>
        <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
        <result column="DUALENTRY_STATUS" jdbcType="VARCHAR" property="dualentryStatus"/>
        <result column="DUALENTRY_FINISH_DTM" jdbcType="TIMESTAMP" property="dualentryFinishDtm"/>
        <result column="DUALENTRY_INTERPOSE_FLAG" jdbcType="VARCHAR" property="dualentryInterposeFlag"/>
        <result column="CALLBACK_STATUS" jdbcType="VARCHAR" property="callbackStatus"/>
        <result column="CALLBACK_FINISH_DTM" jdbcType="TIMESTAMP" property="callbackFinishDtm"/>
        <result column="CALLBACK_INTERPOSE_FLAG" jdbcType="VARCHAR" property="callbackInterposeFlag"/>
        <result column="CALMDTM_INTERPOSE_FLAG" jdbcType="VARCHAR" property="calmdtmInterposeFlag"/>
        <result column="ASSETCERTIFICATE_STATUS" jdbcType="VARCHAR" property="assetcertificateStatus"/>
        <result column="ASSET_INTERPOSE_FLAG" jdbcType="VARCHAR" property="assetInterposeFlag"/>
        <result column="TA_CODE" jdbcType="VARCHAR" property="taCode"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
        <result column="NOTIFY_SUBMIT_FLAG" jdbcType="CHAR" property="notifySubmitFlag"/>
        <result column="SUBMIT_APP_FLAG" jdbcType="CHAR" property="submitAppFlag"/>
        <result column="RET_DESC" jdbcType="VARCHAR" property="retDesc"/>
        <result column="CANCEL_ORDER_SRC" jdbcType="CHAR" property="cancelOrderSrc"/>
        <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
    </resultMap>
    <select id="selectSubmitDtl" parameterType="map" resultMap="HighDealSubmitResultMap">
        SELECT T.DEAL_NO,
               T.DEAL_DTL_NO,
               T.TX_ACCT_NO,
               A.CUST_NAME,
               A.ID_NO,
               A.ID_TYPE,
               A.BANK_ACCT,
               A.INVST_TYPE,
               T.M_BUSI_CODE,
               T.FUND_CODE,
               T.FUND_NAME,
               T.APP_AMT,
               T.APP_VOL,
               A.APP_DATE,
               A.APP_TIME,
               T.TA_TRADE_DT,
               T.CALM_DTM,
               T.TX_APP_FLAG,
               T.SUBMIT_TA_DT,
               T.DUALENTRY_STATUS,
               T.DUALENTRY_FINISH_DTM,
               T.DUALENTRY_INTERPOSE_FLAG,
               T.CALLBACK_STATUS,
               T.CALLBACK_INTERPOSE_FLAG,
               T.CALLBACK_FINISH_DTM,
               T.CALMDTM_INTERPOSE_FLAG,
               T.ASSETCERTIFICATE_STATUS,
               T.ASSET_INTERPOSE_FLAG,
               T.TA_CODE,
               T.PRODUCT_CHANNEL,
               T.NOTIFY_SUBMIT_FLAG,
               A.SUBMIT_APP_FLAG,
               A.RET_DESC,
               A.SUBMIT_DEAL_NO,
               A.CANCEL_ORDER_SRC
        FROM SIMU_FUND_CHECK_ORDER A
                 INNER JOIN HIGH_DEAL_ORDER_DTL T ON A.DEAL_NO = T.DEAL_NO
        WHERE A.SUBMIT_APP_FLAG = '2'
          AND (A.CANCEL_ORDER_SRC IS NULL OR A.CANCEL_ORDER_SRC = '1' OR A.CANCEL_ORDER_SRC = '2')
          AND A.PRODUCT_CHANNEL = #{productChannel,jdbcType=VARCHAR}
          AND A.TRADE_DT = #{tradeDt,jdbcType=VARCHAR}
          AND A.TA_CODE = #{taCode,jdbcType=VARCHAR}
          AND A.M_BUSI_CODE NOT IN ('1366', '1365')
    </select>

    <select id="selectCheckOrdersByAckDtAndTaCode" resultMap="BaseResultMap">
        select DEAL_NO, FUND_TYPE
        from SIMU_FUND_CHECK_ORDER
        where ACK_IMPORT_DT = #{tradeDt, jdbcType = VARCHAR}
          AND TA_CODE = #{taCode,jdbcType=VARCHAR}
    </select>

    <select id="selectCheckOrdersByAckDtAndFundCodes" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        from SIMU_FUND_CHECK_ORDER
        where ACK_IMPORT_DT = #{tradeDt, jdbcType = VARCHAR}
          AND FUND_CODE in
        <foreach collection="fundCodes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <resultMap id="RedeemExpireResultMap" type="com.howbuy.tms.high.batch.dao.vo.ExpireRedeemVo">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
        <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="DEAL_DTL_NO" jdbcType="VARCHAR" property="dealDtlNo"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="DIS_TX_ACCT_NO" jdbcType="VARCHAR" property="disTxAcctNo"/>
        <result column="TX_CHANNEL" jdbcType="CHAR" property="txChannel"/>
        <result column="OUTLET_CODE" jdbcType="VARCHAR" property="outletCode"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="SUB_TX_ACCT_NO" jdbcType="VARCHAR" property="subTxAcctNo"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="INVST_TYPE" jdbcType="CHAR" property="invstType"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="ID_TYPE" jdbcType="CHAR" property="idType"/>
        <result column="ID_NO" jdbcType="VARCHAR" property="idNo"/>
        <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode"/>
        <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName"/>
        <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType"/>
        <result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType"/>
        <result column="FUND_SHARE_CLASS" jdbcType="CHAR" property="fundShareClass"/>
        <result column="APP_DATE" jdbcType="VARCHAR" property="appDate"/>
        <result column="APP_TIME" jdbcType="VARCHAR" property="appTime"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol"/>
        <result column="ACK_DT" jdbcType="VARCHAR" property="ackDt"/>
        <result column="NAV" jdbcType="DECIMAL" property="nav"/>
        <result column="FEE" jdbcType="DECIMAL" property="fee"/>
        <result column="T_FUND_CODE" jdbcType="VARCHAR" property="tFundCode"/>
        <result column="T_FUND_NAME" jdbcType="VARCHAR" property="tFundName"/>
        <result column="T_FUND_SHARE_CLASS" jdbcType="CHAR" property="tFundShareClass"/>
        <result column="TX_RATIO" jdbcType="CHAR" property="txRatio"/>
        <result column="TX_STATUS" jdbcType="CHAR" property="txStatus"/>
        <result column="TX_COMPLETE_DT" jdbcType="VARCHAR" property="txCompleteDt"/>
        <result column="LARGE_REDM_FLAG" jdbcType="CHAR" property="largeRedmFlag"/>
        <result column="RISK_FLAG" jdbcType="CHAR" property="riskFlag"/>
        <result column="FUND_DIV_MODE" jdbcType="CHAR" property="fundDivMode"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="REDEEM_DIRECTION" jdbcType="CHAR" property="redeemDirection"/>
        <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate"/>
        <result column="CANCEL_ORDER_SRC" jdbcType="CHAR" property="cancelOrderSrc"/>
        <result column="BUSI_CODE" jdbcType="CHAR" property="busiCode"/>
        <result column="M_BUSI_CODE" jdbcType="CHAR" property="mBusiCode"/>
        <result column="TX_APP_FLAG" jdbcType="CHAR" property="txAppFlag"/>
        <result column="SUBMIT_APP_FLAG" jdbcType="CHAR" property="submitAppFlag"/>
        <result column="TX_COMP_FLAG" jdbcType="CHAR" property="txCompFlag"/>
        <result column="MEMO" jdbcType="VARCHAR" property="memo"/>
        <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm"/>
        <result column="CREATE_DTM" jdbcType="TIMESTAMP" property="createDtm"/>
        <result column="IP_ADDRESS" jdbcType="VARCHAR" property="ipAddress"/>
        <result column="TA_CODE" jdbcType="VARCHAR" property="taCode"/>
        <result column="T_TA_CODE" jdbcType="VARCHAR" property="tTaCode"/>
        <result column="RET_CODE" jdbcType="VARCHAR" property="retCode"/>
        <result column="RET_DESC" jdbcType="VARCHAR" property="retDesc"/>
        <result column="APP_RATIO" jdbcType="DECIMAL" property="appRatio"/>
        <result column="DEAL_TYPE" jdbcType="VARCHAR" property="dealType"/>
        <result column="PROTOCOL_NO" jdbcType="VARCHAR" property="protocolNo"/>
        <result column="TRANSACTOR_ID_NO" jdbcType="VARCHAR" property="transactorIdNo"/>
        <result column="TRANSACTOR_ID_TYPE" jdbcType="VARCHAR" property="transactorIdType"/>
        <result column="TRANSACTOR_NAME" jdbcType="VARCHAR" property="transactorName"/>
        <result column="UNUSUAL_TRANS_TYPE" jdbcType="VARCHAR" property="unusualTransType"/>
        <result column="PAYMENT_TYPE" jdbcType="CHAR" property="paymentType"/>
        <result column="SUB_BANK_NAME" jdbcType="VARCHAR" property="subBankName"/>
        <result column="INTEREST" jdbcType="DECIMAL" property="interest"/>
        <result column="ACHIEVEMENT_PAY" jdbcType="DECIMAL" property="achievementPay"/>
        <result column="ACHIEVEMENT_COMPEN" jdbcType="DECIMAL" property="achievementCompen"/>
        <result column="VOL_BY_INTEREST" jdbcType="DECIMAL" property="volByInterest"/>
        <result column="RECUPERATE_FEE" jdbcType="DECIMAL" property="recuperateFee"/>
        <result column="FROM_TA_FLAG" jdbcType="CHAR" property="fromTaFlag"/>
        <result column="REFUND_AMT" jdbcType="DECIMAL" property="refundAmt"/>
        <result column="CUST_RISK_LEVEL" jdbcType="VARCHAR" property="custRiskLevel"/>
        <result column="FUND_RISK_LEVEL" jdbcType="VARCHAR" property="fundRiskLevel"/>
        <result column="TRADE_DT" jdbcType="VARCHAR" property="tradeDt"/>
        <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
        <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
        <result column="ACK_IMPORT_DT" jdbcType="VARCHAR" property="ackImportDt"/>
        <result column="RETRIEVE_DTM" jdbcType="TIMESTAMP" property="retrieveDtm"/>
        <result column="DIS_DEAL_ACK_NO" jdbcType="VARCHAR" property="disDealAckNo"/>
        <result column="Is_Redeem_Expire" jdbcType="VARCHAR" property="isRedeemExpire"/>
    </resultMap>

    <select id="selectPurchaseAckSuccForRedeemExpire" parameterType="map" resultMap="RedeemExpireResultMap">
        select A.SUBMIT_DEAL_NO,
               A.DEAL_NO,
               A.DEAL_DTL_NO,
               A.CONTRACT_NO,
               A.TX_ACCT_NO,
               A.DIS_CODE,
               A.DIS_TX_ACCT_NO,
               A.TX_CHANNEL,
               A.OUTLET_CODE,
               A.CP_ACCT_NO,
               A.SUB_TX_ACCT_NO,
               A.BANK_ACCT,
               A.BANK_CODE,
               A.INVST_TYPE,
               A.CUST_NAME,
               A.ID_TYPE,
               A.ID_NO,
               A.FUND_CODE,
               A.FUND_NAME,
               A.FUND_TYPE,
               A.FUND_SUB_TYPE,
               A.FUND_SHARE_CLASS,
               A.APP_DATE,
               A.APP_TIME,
               A.APP_AMT,
               A.APP_VOL,
               A.ACK_AMT,
               A.ACK_VOL,
               A.ACK_DT,
               A.NAV,
               A.FEE,
               A.T_FUND_CODE,
               A.T_FUND_NAME,
               A.T_FUND_SHARE_CLASS,
               A.TX_RATIO,
               A.TX_STATUS,
               A.TX_COMPLETE_DT,
               A.LARGE_REDM_FLAG,
               A.RISK_FLAG,
               A.FUND_DIV_MODE,
               A.TA_TRADE_DT,
               A.REDEEM_DIRECTION,
               A.DISCOUNT_RATE,
               A.CANCEL_ORDER_SRC,
               A.BUSI_CODE,
               A.M_BUSI_CODE,
               A.TX_APP_FLAG,
               A.SUBMIT_APP_FLAG,
               A.TX_COMP_FLAG,
               A.MEMO,
               A.UPDATE_DTM,
               A.CREATE_DTM,
               A.IP_ADDRESS,
               A.TA_CODE,
               A.T_TA_CODE,
               A.RET_CODE,
               A.RET_DESC,
               A.APP_RATIO,
               A.DEAL_TYPE,
               A.PROTOCOL_NO,
               A.TRANSACTOR_ID_NO,
               A.TRANSACTOR_ID_TYPE,
               A.TRANSACTOR_NAME,
               A.UNUSUAL_TRANS_TYPE,
               A.PAYMENT_TYPE,
               A.SUB_BANK_NAME,
               A.INTEREST,
               A.ACHIEVEMENT_PAY,
               A.ACHIEVEMENT_COMPEN,
               A.VOL_BY_INTEREST,
               A.RECUPERATE_FEE,
               A.FROM_TA_FLAG,
               A.REFUND_AMT,
               A.CUST_RISK_LEVEL,
               A.FUND_RISK_LEVEL,
               A.TRADE_DT,
               A.APP_DTM,
               A.SUBMIT_TA_DT,
               A.PRODUCT_CHANNEL,
               A.ACK_IMPORT_DT,
               A.RETRIEVE_DTM,
               A.DIS_DEAL_ACK_NO,
               T.Is_Redeem_Expire
        from SIMU_FUND_CHECK_ORDER A
                 inner JOIN (select deal_no, Is_Redeem_Expire
                             from HIGH_DEAL_ORDER_DTL
                             where Is_Redeem_Expire is not null) t on A.deal_no = t.deal_no
        where A.ACK_IMPORT_DT = #{ackImportDt,jdbcType=VARCHAR}
          and A.ACK_VOL <![CDATA[>]]> 0
          and A.M_BUSI_CODE in ('1120', '1122')
    </select>

    <!-- 查询需要上报的份额合并/迁移对账订单信息 -->
    <select id="selectShareTransferCheckOrdersToSubmit" resultMap="BaseResultMap">
        <!-- 查询48小时以内待上报的对账明细 -->
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        from SIMU_FUND_CHECK_ORDER
    <![CDATA[ 
        where SUBMIT_APP_FLAG in ('1', '3')
          AND M_BUSI_CODE IN ('1365', '1366')
          AND CREATE_DTM >= #{startDtm,jdbcType=TIMESTAMP}
          AND UPDATE_DTM <= DATE_SUB(NOW(), INTERVAL 30 SECOND)
        ]]>
    </select>

    <select id="selectForceRedeemMemoIsEmptyByAckDt" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        from SIMU_FUND_CHECK_ORDER A
        where A.ACK_DT = #{startTradeDt, jdbcType=VARCHAR}
          and A.M_BUSI_CODE = '1145'
    </select>

    <update id="batchUpdateForceRedeemMemo" parameterType="map">
        UPDATE SIMU_FUND_CHECK_ORDER t1
            JOIN (
        SELECT DIS_DEAL_ACK_NO, FORCE_REDEEM_MEMO
        FROM (
        <foreach collection="forceRedeemMemoList" item="item" separator="union all">
            SELECT #{item.disDealAckNo, jdbcType=VARCHAR}    AS DIS_DEAL_ACK_NO,
                   #{item.forceRedeemMemo, jdbcType=VARCHAR} AS FORCE_REDEEM_MEMO
        </foreach>
        ) AS t2
        ) AS t2
            ON t1.DIS_DEAL_ACK_NO = t2.DIS_DEAL_ACK_NO
        SET t1.FORCE_REDEEM_MEMO = t2.FORCE_REDEEM_MEMO
        WHERE t1.M_BUSI_CODE = '1145'
    </update>


    <select id="selectAckRefundList" resultMap="BaseResultMap" parameterType="map">
        select A.*
        from SIMU_FUND_CHECK_ORDER A
                 INNER JOIN DEAL_ORDER_REFUND B ON A.DEAL_NO = B.DEAL_NO
        where A.ACK_DT = #{ackDt, jdbcType=VARCHAR}
          AND A.BUSI_CODE = '024'
          AND A.TX_RATIO IN ('1', '3')
          AND A.TX_STATUS = '3'
    </select>

    <select id="getSimuFundCheckOrderBySplit" resultMap="BaseResultMap" parameterType="map">
        select a.*
        from SIMU_FUND_CHECK_ORDER a
                 INNER JOIN HIGH_DEAL_ORDER_DTL b on a.DEAL_NO = b.DEAL_NO
                 INNER JOIN DEAL_ORDER c on a.DEAL_NO = c.DEAL_NO
        where b.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          and b.FUND_CODE = #{fundCode,jdbcType=VARCHAR}
          and c.CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR}
          and b.ACK_DT = #{ackDt,jdbcType=VARCHAR}
    </select>

    <select id="totalSimuFundCheckOrder" resultType="java.math.BigDecimal">
        SELECT SUM(ifnull(t.app_amt, 0)) AS total_amt
        FROM simu_fund_check_order t
        WHERE t.busi_code IN ('020', '022')
          AND t.submit_ta_dt = #{taDate,jdbcType=VARCHAR}
          AND t.tx_app_flag = '0'
          AND t.payment_type IN
        <foreach collection="paymentTypes" index="index" item="paymentType" open="(" separator="," close=")">
            #{paymentType,jdbcType=VARCHAR}
        </foreach>
    </select>

    <resultMap id="totalHighRedeemVolResultMap" type="com.howbuy.tms.high.batch.dao.vo.TotalHighRedeemVo">
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode"/>
        <result column="redeem_direction" jdbcType="VARCHAR" property="redeemDirection"/>
        <result column="total_vol" jdbcType="DECIMAL" property="totalVol"/>
    </resultMap>
    <select id="totalHighRedeemVol" resultMap="totalHighRedeemVolResultMap" parameterType="map">
        SELECT t.fund_code
             , t.redeem_direction
             , SUM(ifnull(t.app_vol, 0)) AS total_vol
        FROM simu_fund_check_order t
        WHERE t.busi_code = '024'
          AND t.submit_ta_dt = #{taDate,jdbcType=VARCHAR}
          AND t.tx_app_flag = '0'
          AND t.redeem_direction IN
        <foreach collection="redeemDirections" index="index" item="redeemDirectionCode" open="(" separator=","
                 close=")">
            #{redeemDirectionCode,jdbcType=CHAR}
        </foreach>
        GROUP BY t.fund_code, t.redeem_direction
    </select>

    <select id="selectByTradeDtAndTaCode" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper.Base_Column_List"/>
        from SIMU_FUND_CHECK_ORDER A
        where A.ta_code = #{taCode, jdbcType=VARCHAR}
          and A.m_busi_code in ('1120', '1122', '1124', '1136', '1129', '1139', '1149', '1365', '1366')
          and A.ACK_IMPORT_DT = #{tradeDt,jdbcType = VARCHAR}
    </select>

    <resultMap id="DealPmtResultMap" type="com.howbuy.tms.high.batch.dao.vo.DealPmtVo">
        <result column="PMT_DEAL_NO" jdbcType="VARCHAR" property="pmtDealNo"/>
        <result column="SUBMIT_DEAL_NO" jdbcType="VARCHAR" property="submitDealNo"/>
        <result column="TX_APP_FLAG" jdbcType="VARCHAR" property="txAppFlag"/>
    </resultMap>

    <select id="getBySubmitDealNos" resultMap="DealPmtResultMap" parameterType="map">
        select p.pmt_deal_no, t.submit_deal_no, t.tx_app_flag from (
        select *
        from simu_fund_check_order
        where submit_deal_no in
        <foreach collection="submitDealNos" item="submitDealNo" index="index"
                 open="(" close=")" separator=",">
            #{submitDealNo,jdbcType=VARCHAR}
        </foreach>
        ) t,
            payment_order p
        where t.deal_no = p.deal_no
    </select>

    <resultMap id="OrderSumGroupResultMap" type="com.howbuy.tms.high.batch.dao.vo.OrderSumGroupTaVo">
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
        <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
        <result column="CHECK_TOTAL_COUNT" jdbcType="DECIMAL" property="checkTotalCount" />
        <result column="CHECK_TOTAL_AMT" jdbcType="DECIMAL" property="checkTotalAmt" />
        <result column="CHECK_TOTAL_VOL" jdbcType="DECIMAL" property="checkTotalVol" />
    </resultMap>
    <select id="getOrderSumGroupTa" resultMap="OrderSumGroupResultMap" parameterType="map">
        SELECT 'CHAN05HMP01' AS CHANNEL_CODE,
        TA_TRADE_DT,
        TA_CODE,
        SUM(CHECK_TOTAL_COUNT) AS CHECK_TOTAL_COUNT,
        SUM(CHECK_TOTAL_AMT) AS CHECK_TOTAL_AMT,
        SUM(CHECK_TOTAL_VOL) AS CHECK_TOTAL_VOL
        FROM (
        SELECT ${taTradeDt} AS TA_TRADE_DT,
        A.TA_CODE,
        COUNT(1) AS CHECK_TOTAL_COUNT,
        ifnull(SUM(A.APP_AMT), 0) AS CHECK_TOTAL_AMT,
        ifnull(SUM(A.APP_VOL), 0) AS CHECK_TOTAL_VOL
        FROM SIMU_FUND_CHECK_ORDER A
        WHERE A.SUBMIT_TA_DT = #{taTradeDt, jdbcType = VARCHAR}
        AND A.TX_APP_FLAG = '0'
        And A.TX_COMP_FLAG = '2'
        AND A.SUBMIT_APP_FLAG = '2'
        AND A.TA_CODE IN
        <foreach collection="taCodeList" item="taCode" index="index" open="(" close=")" separator=",">
            #{taCode,jdbcType=VARCHAR}
        </foreach>
        GROUP BY A.TA_CODE) hh
        GROUP BY TA_CODE,TA_TRADE_DT
    </select>
</mapper>
/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.importpaychkfile;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.high.batch.dao.po.batch.BusinessBatchFlowPo;
import com.howbuy.tms.high.batch.facade.trade.importpaychkfile.HighImportPayChkFileFacade;
import com.howbuy.tms.high.batch.facade.trade.importpaychkfile.HighImportPayChkFileRequest;
import com.howbuy.tms.high.batch.facade.trade.importpaychkfile.HighImportPayChkFileResponse;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.trade.importpaychkfile.task.ImportPayChkTask;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.ImportPayChkRepository;
import com.howbuy.tms.high.batch.service.service.batch.importpaychk.ImportPayChkService;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @description:导入支付对账文件服务类
 * <AUTHOR>
 * @date 2017年7月6日 下午5:03:36
 * @since JDK 1.6
 */
@DubboService
@Service("highImportPayChkFileFacadeService")
public class HighImportPayChkFileFacadeService implements HighImportPayChkFileFacade {

    private static Logger logger = LogManager.getLogger(HighImportPayChkFileFacadeService.class);

    @Autowired
    private ImportPayChkService importPayChkService;

    @Autowired
    private ImportPayChkRepository importPayChkRepository;
    
    @Autowired
    private BusinessBatchFlowRepository businessBatchFlowRepository;

    /**
     * 队列容量
     */
    private static final int POOL_CAPACITY = 10240;

    /**HighImportAckFileFacadeService
     * 线程池
     */
    private ExecutorService threadPool = createFixedThreadPool("HighImportPayChkFileFacadeService",2);


    @Override
    public HighImportPayChkFileResponse execute(HighImportPayChkFileRequest request) {
        logger.info("开始执行支付对账文件导入:{}", JSON.toJSONString(request));

        HighImportPayChkFileResponse resp = new HighImportPayChkFileResponse();
        try {
            String tradeDt = request.getTaTradeDt();
            BusinessBatchFlowPo po = businessBatchFlowRepository.selectByTaskIdAndTradeDtAndSysCode(BusinessProcessingStepEnum.BPS_TRADE_APP_DAY_END.getCode(), tradeDt, SysCodeEnum.BATCH_HIGH.getCode());
            if (po != null && BatchStatEnum.PROCESS_SUCCESS.getKey().equals(po.getBatchStat())) {
                throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_PMT_CHECK_ACK_ERROR, "交易申请日终已完成, 不能再导入支付对账确认文件");
            }
            // 导入前先删除
            importPayChkRepository.deletePaymentData(SysCodeEnum.BATCH_HIGH.getCode());
            // 导入，存在导入失败全部删除，否则导入后更新成90
            importFile(tradeDt);
            resp.setReturnCode(ExceptionCodes.SUCCESS);
        } catch (Exception e) {
            logger.error(e);
            resp.setReturnCode(ExceptionCodes.ORDER_CENTER_SYSTEM_ERROR);
            resp.setDescription(e.getMessage());
        }
        logger.info("执行支付对账文件导入结束:{}", JSON.toJSONString(resp));
        return resp;
    }
    
    private void importFile(String tradeDt) throws Exception {
        String[] sysCodes = {"93", "94"};
        CountDownLatch latch = new CountDownLatch(2);
        List<ThreadExceptionStatus> exList = new ArrayList<>();
        ThreadExceptionStatus exStatus = null;
        for (String sysCode : sysCodes) {
            exStatus = new ThreadExceptionStatus();
            exStatus.setSysCode(sysCode);
            exList.add(exStatus);
            threadPool.execute(ThreadTraceHelper.decorate(new ImportPayChkTask(latch, exStatus, tradeDt, importPayChkService, sysCode)));
        }
        try {
            latch.await();
        } catch (Exception e) {
            logger.error("HighImportPayChkFileFacadeService|importFile|latch.await error,errMsg:{}", e.getMessage(), e);
        }
        
        StringBuilder retMsg = new StringBuilder();
        boolean flag = false;
        for (ThreadExceptionStatus ex : exList) {
            if (ex.isExsitException()) {
                flag = true;
                if (ex.getException() instanceof BatchException) {
                    BatchException batchEx = (BatchException) ex.getException();
                    retMsg.append(getSysCodeName(ex.getSysCode())).append("处理异常：").append(batchEx.getErrorCode()).append(batchEx.getMessage()).append("\n");
                } else {
                    retMsg.append(getSysCodeName(ex.getSysCode())).append("支付对账文件导入系统异常").append("\n");
                }
            }
        }
        if (flag) {
            // 存在异常则删除导入数据
            importPayChkRepository.deletePaymentData("93");
            importPayChkRepository.deletePaymentData("94");
            throw new Exception(retMsg.toString());
        } else {
            // 没有异常更新成90
            importPayChkRepository.updateSysCode(SysCodeEnum.BATCH_HIGH.getCode());
        }
    }
    
    private String getSysCodeName(String sysCode) {
        if ("93".equals(sysCode)) {
            return "高端公募";
        } else if ("94".equals(sysCode)) {
            return "私募";
        }
        return "";
    }

    public static ExecutorService createFixedThreadPool(String threadName, int poolSize) {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadName).build();
        return new ThreadPoolExecutor(poolSize, poolSize,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(POOL_CAPACITY), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }

}

package com.howbuy.tms.high.batch.service.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.tms.high.batch.dao.mapper.customize.order.EsProOrderMonitorPoMapper;
import com.howbuy.tms.high.batch.dao.po.order.EsProOrderMonitorPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EsProOrderMonitorRepository {
    @Autowired
    private EsProOrderMonitorPoMapper esProOrderMonitorPoMapper;

    /**
     *
     * fetchNoSynOrders:分页查询未同步订单
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2019年2月14日 下午2:24:52
     */
    public Page<EsProOrderMonitorPo> fetchNoSynOrders(Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        return esProOrderMonitorPoMapper.fetchNoSynOrders();
    }

    public int batchInsert(List<EsProOrderMonitorPo> esList) {
           return esProOrderMonitorPoMapper.batchInsert(esList);
    }

    public void insertSelective(EsProOrderMonitorPo monitor) {
        esProOrderMonitorPoMapper.insertSelective(monitor);
    }

    public void deleteByDealNo(String dealNo) {
        esProOrderMonitorPoMapper.deleteByDealNo(dealNo);
    }
}

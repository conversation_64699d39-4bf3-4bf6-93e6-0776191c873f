package com.howbuy.tms.high.batch.dao.mapper.customize.order;



import com.github.pagehelper.Page;
import com.howbuy.tms.high.batch.dao.mapper.order.SimuFundCheckOrderPoAutoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.*;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
/**
 * 去O
 */
public interface SimuFundCheckOrderPoMapper extends SimuFundCheckOrderPoAutoMapper {
    /**
     * countDealNotCompDeal:交易未对账交易数量
     * 
     * @param taTradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午5:55:36
     */
    Integer countDealNotCompDeal(@Param("taTradeDt") String taTradeDt, @Param("taCodes") List<String> taCodes);
    List<String> queryDealNotCompDeal(@Param("taTradeDt") String taTradeDt, @Param("taCodes") List<String> taCodes);

    /**
     * countExCompDeal:交易对账异常未处理交易
     * 
     * @param taTradeDt
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午5:56:13
     */
    Integer countExCompDeal(@Param("taTradeDt") String taTradeDt, @Param("productChannel") String productChannel);

    /**
     * 
     * updateReSubmittingByDealDtlNo:重新上报时，设置各标记
     * 
     * @param record
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午3:52:44
     */
    int updateReSubmittingByDealDtlNo(SimuFundCheckOrderPo record);

    /**
     * 
     * updateNotNeedSubmitByDealDtlNo:更新为无需上报
     * 
     * @param record
     * @return
     * <AUTHOR>
     * @date 2018年12月6日 上午10:29:28
     */
    int updateNotNeedSubmitByDealDtlNo(SimuFundCheckOrderPo record);

    /**
     * 
     * selectUnCheckOrderWithPage:查询交易上报完成对账状态为未对账、最后更新时间再3分钟之前的交易对账订单信息，
     * 如果是申购类订单需要支付对账标记为对账完成
     * 
     * @param tradeDt
     * @param startDtm
     * @param endDtm
     * @param lastUpdateDtm
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午5:22:36
     */
    Page<SimuFundCheckOrderPo> selectUnCheckOrderWithPage(@Param("tradeDt") String tradeDt, @Param("startDtm") Date startDtm,
                                                          @Param("endDtm") Date endDtm, @Param("lastUpdateDtm") Date lastUpdateDtm);

    /**
     * 
     * selectByDealDtlNo:根据交易明细订单号查询私募对账订单信息
     * 
     * @param dealDtlNo
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午1:34:24
     */
    List<SimuFundCheckOrderPo> selectByDealDtlNo(@Param("dealDtlNo") String dealDtlNo);

    /**
     * 
     * selectByAckImportDtAndProductChannel:(查询需生成份额确认书的当日确认订单)
     * 
     * @param ackImportDt
     * @return
     * <AUTHOR>
     * @date 2018年2月12日 下午2:28:08
     */
    List<SimuFundCheckOrderDto> selectNeedGenVolConfirmBookByAckImportDt(@Param("ackImportDt") String ackImportDt);

    SimuFundCheckOrderPo selectTotalAmountByMainDealNo(@Param("mainDealNo") String mainDealNo);
    /**
     * 
     * selectCheckOrdersToSubmit: 查询需要上报的对账订单信息
     * 
     * @param tradeDt
     * @param now 当前时间
     * @param submitRetrieveCtr 检索时间控制 0-不控制 1-控制
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午2:11:09
     */
    List<SimuFundCheckOrderPo> selectCheckOrdersToSubmit(@Param("tradeDt") String tradeDt, @Param("now") Date now,
                                                         @Param("submitRetrieveCtr") String submitRetrieveCtr);

    /**
     * 
     * updateAppFlagOrCompFlagBySubmitDealNo:根据上报申请订单号更新对账订单中的交易申请标记或者交易对账标记
     * 
     * @param submitDealNo
     *            上报申请订单号
     * @param txAppFlag
     *            交易申请标记
     * @param txCompFlag
     *            交易对账状态
     * @param submitAppFlag
     *            交易上报标记
     * @param cancelOrderSrc
     *            撤销来源
     * @param now
     *            时间戳
     * @param oldUpdateDtm
     *            上次更新时间戳
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午2:22:04
     */
    int updateAppFlagOrCompFlagBySubmitDealNo(@Param("submitDealNo") String submitDealNo, @Param("txAppFlag") String txAppFlag,
            @Param("txCompFlag") String txCompFlag, @Param("submitAppFlag") String submitAppFlag, @Param("cancelOrderSrc") String cancelOrderSrc,
            @Param("now") Date now, @Param("oldUpdateDtm") Date oldUpdateDtm);

    /**
     * 分页查询私募上报交易
     * @param condition
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo>
     * @author: huaqiang.liu
     * @date: 2021/5/31 17:04
     * @since JDK 1.8
     */
    Page<SimuFundCheckOrderPo> selectSimuFundCheckOrderWithPage(@Param("condition") SimuFundCheckOrderPo condition);

    /***
     *
     * selectSimuFundCheckOrderForConsole:(中控台-私募交易申请查询)
     *
     * @param condition
     * @param appDateStart
     * @param appDateEnd
     * @param taTradeDtStart
     * @param taTradeDtEnd
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午2:57:20
     */
    Page<SimuFundCheckOrderDto> selectSimuFundCheckOrderForConsole(@Param("condition") SimuFundCheckOrderPo condition,
       @Param("appDateStart") String appDateStart, @Param("appDateEnd") String appDateEnd, @Param("taTradeDtStart") String taTradeDtStart,
       @Param("taTradeDtEnd") String taTradeDtEnd, @Param("mergeSubmitFlag") String mergeSubmitFlag, @Param("filterFundCodeList") List<String> filterFundCodeList);

    /***
     * 
     * selectCountSimuFundCheckOrderForConsole:(中控台-私募交易申请统计)
     * 
     * @param condition
     * @param appDateStart
     * @param appDateEnd
     * @param taTradeDtStart
     * @param taTradeDtEnd
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午3:09:33
     */
    SimuFundCheckOrderPo selectCountSimuFundCheckOrderForConsole(@Param("condition") SimuFundCheckOrderPo condition, @Param("appDateStart") String appDateStart,
            @Param("appDateEnd") String appDateEnd, @Param("taTradeDtStart") String taTradeDtStart, @Param("taTradeDtEnd") String taTradeDtEnd,@Param("filterFundCodeList") List<String> filterFundCodeList);

    /***
     * 
     * selectDealCheckOrderByTaTradeDateAndTxCompFlag:(通过对账状态查询交易对账状态)
     * 
     * @param taTradeDt
     * @param txCompFlag
     * @param filterFundCodeList
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午7:40:31
     */
    Page<DealCheckOrderVo> selectDealCheckOrderByTaTradeDateAndTxCompFlag(@Param("filterFundCodeList")List<String> filterFundCodeList,@Param("taTradeDt") String taTradeDt, @Param("txCompFlag") String txCompFlag);

    /***
     * 
     * sumAppAmt:(汇总申请金额)
     * 
     * @param taTradeDt
     * @param txCompFlag
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午7:42:22
     */
    BigDecimal sumAppAmt(@Param("taTradeDt") String taTradeDt, @Param("txCompFlag") String txCompFlag);

    /**
     * 
     * selectByGtTaTradeDt:查询大于等于指定TA工作日的上报成功的上报订单
     * 
     * @param taTradeDt
     *            TA工作日
     * @return
     * @return List<SimuFundCheckOrderVo>
     * <AUTHOR>
     * @date 2017年7月28日 上午10:51:21
     */
    List<SimuFundCheckOrderVo> selectByGtTaTradeDt(@Param("taTradeDt") String taTradeDt);

    /**
     * 
     * updateTaTradeDt:更新上报订单TA工作日
     * 
     * @param submitDealNo
     *            上报订单号
     *            新TA工作日
     * @param oldSubmitTaDt
     *            原TA工作日
     * @return
     * @return int
     * <AUTHOR>
     * @date 2017年7月28日 上午10:52:59
     */
    int updateSubmitTaDt(@Param("submitDealNo") String submitDealNo, @Param("newSubmitTaDt") String newSubmitTaDt, @Param("oldSubmitTaDt") String oldSubmitTaDt,
            @Param("busiCode") String busiCode, @Param("mBusiCode") String mBusiCode);

    /**
     * 
     * countFundCheckByTradeDt:根据tradeDt查询上报成功汇总信息
     * 
     * @param tradeDt
     * @return
     * <AUTHOR>
     * @date 2017年8月25日 下午1:24:02
     */
    TradeCheckCountVo countFundCheckByTradeDt(@Param("tradeDt") String tradeDt);

    /**
     * 
     * selectFundCheckByTradeDt:根据tradeDt查询上报成功明细信息
     * 
     * @param tradeDt
     * @return
     * <AUTHOR>
     * @date 2017年8月25日 下午2:06:33
     */
    @MapKey("submitDealNo")
    Map<String, TradeCheckDetailVo> selectFundCheckByTradeDt(@Param("tradeDt") String tradeDt);

    /**
     * updateSubmitFlagWithForce:(重置强制取消的上报订单的上报标识)
     * 
     * @param dealNo
     * @param simuFundCheckOrderPo
     * @return
     * <AUTHOR>
     * @date 2017年11月21日 下午2:24:09
     */
    int updateSubmitFlagWithForce(@Param("dealNo") String dealNo, @Param("simuFundCheckOrder") SimuFundCheckOrderPo simuFundCheckOrderPo);
    
    /**
     * 
     * updateRetrieveDtm:(根据上报订单号更新检索时间)
     * @param submitDealNo
     * @param retrieveDtm
     * @return
     * <AUTHOR>
     * @date 2018年5月30日 下午2:11:32
     */
    int updateRetrieveDtm(@Param("submitDealNo") String submitDealNo, @Param("retrieveDtm") Date retrieveDtm);
    
    /**
     * 
     * selectFundCheckByDisDealAckNo:查询交易上报需要修改赎回方向的数据
     * 
     * @param disDealAckNoList
     * @return
     * <AUTHOR>
     * @date 2018年5月30日 下午3:15:59
     */
    @MapKey("disDealAckNo")
    Map<String, SimuFundCheckOrderPo> selectFundCheckByDisDealAckNo(@Param("disDealAckNoList") List<String> disDealAckNoList);

    /**
     * 
     * updateRedeemDirection:更新汇款方向
     * 
     * @param dealNo
     * @param redeemDirection
     * @return
     * <AUTHOR>
     * @date 2018年5月30日 下午3:54:04
     */
    int updateRedeemDirection(@Param("dealNo") String dealNo, @Param("redeemDirection") String redeemDirection);

    /**
     * 
     * countSubmitingOrReSubmit:上报中或重新上报订单
     * 
     * @param tradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:51:18
     */
    Integer countSubmitingOrReSubmit(@Param("tradeDt") String tradeDt, @Param("taCodes") List<String> taCodes);
    List<String> querySubmitingOrReSubmit(@Param("tradeDt") String tradeDt, @Param("taCodes") List<String> taCodes);

    /**
     * 查询时间区间范围内的上报中或者重新上报的订单
     * @param signStartDt 开始时间(包)
     * @param endTradeDt 结束时间(包)
     * @param taCodes 机构code
     */
    List<String> querySubmitingOrReSubmitByDt(@Param("signStartDt") String signStartDt,@Param("endTradeDt") String endTradeDt, @Param("taCodes") List<String> taCodes);


    /**
     * 
     * selectSubmitingOrReSubmit:上报中或重新上报订单
     * 
     * @param tradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:51:18
     */
    List<SimuFundCheckOrderPo> selectSubmitingOrReSubmit(@Param("tradeDt") String tradeDt, @Param("taCodes") List<String> taCodes);

    List<SimuFundCheckOrderPo> selectSubmitingOrReSubmitByDt(@Param("signStartDt") String signStartDt,@Param("endTradeDt") String tradeDt, @Param("taCodes") List<String> taCodes);
    
    /**
     * 
     * selectListDealNotCompDeal:(TODO 这里用一句话描述这个方法的作用)
     * 
     * @param taTradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午8:48:58
     */
    List<SimuFundCheckOrderPo> selectListDealNotCheck(@Param("taTradeDt") String taTradeDt, @Param("taCodes") List<String> taCodes);

    /**
     * 
     * updateSubmitTaDtByDealNo:更新上报日期
     * @param dealNo
     * @param newSubmitTaDt
     * @param oldSubmitTaDt
     * @param updateDtm
     * @return
     * <AUTHOR>
     * @date 2018年6月7日 下午4:48:52
     */
    int updateSubmitTaDtByDealNo(@Param("dealNo") String dealNo, @Param("newSubmitTaDt") String newSubmitTaDt, @Param("oldSubmitTaDt") String oldSubmitTaDt, @Param("updateDtm") Date updateDtm);
    
    /**
     * 
     * selectShareMergeCheckOrdersToSubmit:查询需要上报的份额合并/迁移对账订单信息
     * 
     * @param startDtm
     * @return
     * <AUTHOR>
     * @date 2018年5月14日 下午3:56:10
     */
    List<ShareMergeOrderVo> selectShareMergeCheckOrdersToSubmit(@Param("startDtm") Date startDtm);
    
    /**
     * 
     * selectShareMergeInCheckOrder:查询份额合并/迁移转入上报订单号
     * 
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2018年4月2日 上午10:56:30
     */
    @MapKey("dealDtlNo")
    Map<String, SimuFundCheckOrderPo> selectShareMergeInCheckOrder(@Param("dealNo") String dealNo);
    
    /**
     * 
     * selectShareMergeOutByDealNo:根据订单号查询份额合并/迁移转出上报信息
     * 
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2018年5月18日 下午7:26:27
     */
    List<SimuFundCheckOrderPo> selectShareMergeOutByDealNo(@Param("dealNo") String dealNo);
    
    /**
     * 
     * updateByDealNo:根据主订单号更新上报表
     * @param dealNo
     * @param submitAppFlag
     * @return
     * <AUTHOR>
     * @date 2018年5月21日 下午12:39:30
     */
    int updateByDealNo(@Param("dealNo") String dealNo, @Param("submitAppFlag") String submitAppFlag);
    
    /**
     * 
     * selectShareMergeCheckOrdersToRefershResult:查询已经上报还没确认结果的份额合并/迁移的订单
     * 
     * @param startDtm
     * @return
     * <AUTHOR>
     * @date 2018年5月23日 上午9:47:18
     */
    List<SimuFundCheckOrderPo> selectShareMergeCheckOrdersToRefershResult(@Param("startDtm") Date startDtm);

    /**
     * 
     * selectHighTradeAckDataForConsole:高端交易确认查询
     * 
     * @param condition
     * @param ackDtStart
     * @param ackDtEnd
     * @return
     * <AUTHOR>
     * @date 2018年6月20日 下午7:00:03
     */
    Page<HighTradeAckDataVo> selectHighTradeAckDataForConsole(@Param("condition") SimuFundCheckOrderPo condition,
              @Param("ackDtStart") String ackDtStart, @Param("ackDtEnd") String ackDtEnd, @Param("mergeSubmitFlag") String mergeSubmitFlag,@Param("filterFundCodeList") List<String> filterFundCodeList);
    /**
     * 
     * selectCountHighTradeAckDataForConsole:高端交易确认统计
     * 
     * @param condition
     * @return
     * <AUTHOR>
     * @date 2018年6月20日 下午7:10:05
     */
    HighTradeAckDataVo selectCountHighTradeAckDataForConsole(@Param("condition") SimuFundCheckOrderPo condition, @Param("ackDtStart") String ackDtStart, @Param("ackDtEnd") String ackDtEnd,@Param("filterFundCodeList")List<String> filterFundCodeList);
    /**
     * 
     * countSubmit:上报明细查询
     * @param tradeDt
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 上午10:10:39
     */
    Integer countSubmit(@Param("tradeDt") String tradeDt, @Param("taCode") String taCode, @Param("productChannel") String productChannel);
    /**
     * 
     * selectSubmitDtl:可上报数据查询
     * @param tradeDt
     * @param taCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 上午9:55:53
     */
    List<SimuFundCheckOrderManualSubmitVo> selectCheckOrdersToManualSubmit(@Param("tradeDt") String tradeDt, @Param("taCode") String taCode, @Param("productChannel") String productChannel);

    List<SimuFundCheckOrderManualSubmitVo> selectCheckOrdersToManualSubmitByDealDtlNo(@Param("dealDtlNoList")List<String> dealDtlNoList);
    /**
     * 根据主订单号查询可上报订单
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo>
     * @author: huaqiang.liu
     * @date: 2021/3/11 17:35
     * @since JDK 1.8
     */
    List<SimuFundCheckOrderManualSubmitVo> selectCheckOrdersToManualSubmitByMainDealNo(@Param("mainDealNo") String mainDealNo);

    List<HighDealSubmitDtlVo> selectSubmitDtl(@Param("tradeDt") String tradeDt, @Param("taCode") String taCode, @Param("productChannel") String productChannel);
    /**
     * 
     * selectCheckOrdersByAckDtAndTaCode:根据确认导入日期和TACODE查询
     * @param tradeDt
     * @param taCode
     * @return
     * <AUTHOR>
     * @date 2019年2月2日 下午3:08:22
     */
    List<SimuFundCheckOrderPo> selectCheckOrdersByAckDtAndTaCode(@Param("tradeDt") String tradeDt, @Param("taCode") String taCode);

    List<SimuFundCheckOrderPo> selectCheckOrdersByAckDtAndFundCodes(@Param("tradeDt") String tradeDt, @Param("fundCodes") List<String> fundCodes);

    /**
     *
     * selectPurchaseAckSucc:确认成功,到期赎回订单查询
     * @param ackImportDt
     *
     */
     List<ExpireRedeemVo> selectPurchaseAckSuccForRedeemExpire(@Param("ackImportDt") String ackImportDt);
    
    /**
     * 
     * selectShareTransferCheckOrdersToSubmit:份额迁移
     * @param startDtm
     * @return
     * <AUTHOR>
     * @date 2019年5月22日 下午3:00:31
     */
    List<SimuFundCheckOrderPo> selectShareTransferCheckOrdersToSubmit(@Param("startDtm") Date startDtm);

    /**
     * 
     * @Description 查询指定日期强减原因是空的记录
     * 
     * @param startTradeDt
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo>
     * <AUTHOR>
     * @Date 2020/4/7 10:56
     **/
    Page<SimuFundCheckOrderPo> selectForceRedeemMemoIsEmptyByAckDt(@Param("startTradeDt") String startTradeDt);
    
    /**
     * 
     * @Description 批量更新强减原因
     * @param forceRedeemMemoList
     * @return int
     * <AUTHOR>
     * @Date 2020/4/8 17:41
     **/
    int batchUpdateForceRedeemMemo(@Param("forceRedeemMemoList") List<SimuFundCheckOrderPo> forceRedeemMemoList);

    /**
     * 
     * getByContractNo:根据后台订单号查询私募对账订单信息
     * qiang.tang
     * 2020-06-18
     * 关联交易
     */
    List<SimuFundCheckOrderPo> getByContractNo(@Param("contractNo")String contractNo);

    /**
     * @description:查询多回款方向确认数据
     * @param ackDt
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo>
     * @author: chuanguang.tang
     * @date: 2021/7/23 15:26
     * @since JDK 1.8
     */
    List<SimuFundCheckOrderPo> selectAckRefundList(String ackDt);

    List<SimuFundCheckOrderPo> getSimuFundCheckOrderBySplit(@Param("txAcctNo")String txAcctNo, @Param("fundCode")String fundCode,
                                                            @Param("cpAcctNo")String cpAcctNo, @Param("ackDt")String ackDt);

    BigDecimal totalSimuFundCheckOrder(@Param("taDate")String taDate, @Param("paymentTypes")List<String> paymentTypes);

    List<TotalHighRedeemVo> totalHighRedeemVol(@Param("taDate")String taDate, @Param("redeemDirections")List<String> redeemDirections);

    List<SimuFundCheckOrderPo> selectByTradeDtAndTaCode(@Param("tradeDt")String tradeDt, @Param("taCode")String taCode);

    List<DealPmtVo> getBySubmitDealNos(@Param("submitDealNos")List<String> submitDealNos);

    Collection<? extends OrderSumGroupTaVo> getOrderSumGroupTa(@Param("taTradeDt")String taTradeDt, @Param("taCodeList")List<String> taCodeList);
}
/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.ackdayend;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.high.batch.facade.trade.ackdayend.HighAckDayEndProcessFacade;
import com.howbuy.tms.high.batch.facade.trade.ackdayend.HighAckDayEndProcessRequest;
import com.howbuy.tms.high.batch.facade.trade.ackdayend.HighAckDayEndProcessResponse;
import com.howbuy.tms.high.batch.service.business.redeemsplit.RedeemSplitService;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.trade.task.AckDayEndTask;
import com.howbuy.tms.high.batch.service.service.batch.ackdayend.AckDayEndProcessService;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.tms.high.batch.service.service.order.ackdayend.HighAckDayEndService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:确认日终处理实现类
 * @date 2017年7月10日 下午9:28:49
 * @since JDK 1.6
 */
@DubboService
@Service("highAckDayEndProcessFacadeService")
public class HighAckDayEndProcessFacadeService implements HighAckDayEndProcessFacade {

    private static Logger logger = LogManager.getLogger(HighAckDayEndProcessFacadeService.class);
    @Autowired
    private AckDayEndProcessService ackDayEndProcessService;
    @Autowired
    private WorkdayService workdayService;

    @Autowired
    private HighAckDayEndService highAckDayEndService;
    @Autowired
    private RedeemSplitService redeemSplitService;

    @Override
    public HighAckDayEndProcessResponse execute(HighAckDayEndProcessRequest request) {
        logger.info("开始确认日终处理");
        HighAckDayEndProcessResponse resp = new HighAckDayEndProcessResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        List<String> taCodeList = request.getTaCodeList();
        String sysCode = SysCodeEnum.BATCH_HIGH.getCode();
        if (CollectionUtils.isEmpty(taCodeList)) {
            throw new BatchException(ExceptionCodes.PARAM_IS_NULL, "未选择TA");
        }
        // 处理
        process(taCodeList, tradeDt, sysCode, resp);
        // 检查所有TA业务是否做完，更新主业务表
        checkAllEnd(tradeDt, sysCode);

        logger.info("确认日终执行结束:{}", JSON.toJSONString(resp));
        return resp;
    }

    /**
     * process:多线程处理
     *
     * @param taCodeList
     * @param tradeDt
     * @param sysCode
     * @param resp
     * <AUTHOR>
     * @date 2018年12月5日 下午4:06:50
     */
    private void process(List<String> taCodeList, String tradeDt, String sysCode, HighAckDayEndProcessResponse resp) {
        List<ThreadExceptionStatus> exList = new ArrayList<>();
        ThreadExceptionStatus exStatus = null;
        CountDownLatch latch = new CountDownLatch(taCodeList.size());
        for (String taCode : taCodeList) {
            exStatus = new ThreadExceptionStatus();
            exList.add(exStatus);
            exStatus.setTaCode(taCode);
            CommonThreadPool.execute(new AckDayEndTask(taCode, tradeDt, sysCode, ackDayEndProcessService, highAckDayEndService,
                    redeemSplitService, exStatus, latch));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("latch await error.", e);
            Thread.currentThread().interrupt();
        }
        // 异常处理
        processEx(exList, resp);
    }

    private void processEx(List<ThreadExceptionStatus> resultList, HighAckDayEndProcessResponse resp) {
        BusinessException bizEx = null;
        StringBuffer errorCode = new StringBuffer();
        StringBuffer errorMsg = new StringBuffer();
        for (ThreadExceptionStatus exStatus : resultList) {
            if (exStatus.isExsitException()) {
                if (exStatus.getException() instanceof BusinessException) {
                    bizEx = (BusinessException) exStatus.getException();
                    if (StringUtils.isEmpty(errorCode)) {
                        errorCode.append("TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(bizEx.getErrorCode());
                    } else {
                        errorCode.append(",TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(bizEx.getErrorCode());
                    }
                    errorMsg.append("TA:" + exStatus.getTaCode() + "," + bizEx.getErrorDesc() + "\n");
                } else {
                    if (StringUtils.isEmpty(errorCode)) {
                        errorCode.append("TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(ExceptionCodes.HIGH_BATCH_CENTER_ACK_DAY_END_PROCESS_FAIL);
                    } else {
                        errorCode.append(",TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(ExceptionCodes.HIGH_BATCH_CENTER_ACK_DAY_END_PROCESS_FAIL);
                    }
                    errorMsg.append("TA:" + exStatus.getTaCode() + ",批处理执行失败！确认日终发生异常\n");
                }
            }
        }
        if (!StringUtils.isEmpty(errorCode)) {
            resp.setReturnCode(errorCode.toString());
            resp.setDescription(errorMsg.toString());
        }
    }

    /**
     * checkAllEnd:当所有TA处理完，更新主业务表
     *
     * @param tradeDt
     * @param sysCode
     * <AUTHOR>
     * @date 2018年12月5日 下午4:00:18
     */
    private void checkAllEnd(String tradeDt, String sysCode) {
        ackDayEndProcessService.endAllOff(tradeDt, sysCode);
    }
}

package com.howbuy.tms.high.batch.service.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.howbuy.tms.high.batch.dao.mapper.customize.order.SimuFundCheckOrderPoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPoExample;
import com.howbuy.tms.high.batch.dao.vo.*;
import com.howbuy.tms.high.batch.facade.query.queryfundackfilerec.bean.QueryHighFundAckFileRecCondition;
import com.howbuy.tms.high.batch.facade.query.queryfundcheckorder.bean.QuerySimuFundCheckOrderCondition;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import com.howbuy.tms.high.batch.dao.vo.DealPmtVo;
import com.howbuy.tms.high.batch.dao.vo.TotalHighRedeemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public class SimuFundCheckOrderRepository {
    @Autowired
    private SimuFundCheckOrderPoMapper simuFundCheckOrderPoMapper;

    public BigDecimal totalSimuFundCheckOrder(String taDate, List<String> paymentTypes) {
        return simuFundCheckOrderPoMapper.totalSimuFundCheckOrder(taDate, paymentTypes);
    }

    public List<TotalHighRedeemVo> totalHighRedeemVol(String taDate, List<String> redeemDirections) {
        return simuFundCheckOrderPoMapper.totalHighRedeemVol(taDate, redeemDirections);
    }

    /**
     *
     * getUnCheckOrder:查询交易上报完成对账状态为未对账、最后更新时间再3分钟之前的交易对账订单信息
     *
     * @param startDtm
     * @param endDtm
     * @param pageNo
     * @param pageSize
     * @param isCount
     * @param lastUpdateDtm
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午5:18:36
     */
    public Page<SimuFundCheckOrderPo> getUnCheckOrder(String tradeDt, Date startDtm, Date endDtm, Integer pageNo, Integer pageSize, boolean isCount, Date lastUpdateDtm) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize, isCount);
        }

        return simuFundCheckOrderPoMapper.selectUnCheckOrderWithPage(tradeDt, startDtm, endDtm, lastUpdateDtm);
    }

    /**
     *
     * getBySubmitDealNo:根据上报订单号查询对账订单信息
     *
     * @param submitDealNo
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午5:45:05
     */
    public SimuFundCheckOrderPo getBySubmitDealNo(String submitDealNo) {
        SimuFundCheckOrderPoExample simuFundCheckOrderPoExample = new SimuFundCheckOrderPoExample();
        simuFundCheckOrderPoExample.createCriteria().andSubmitDealNoEqualTo(submitDealNo);
        List<SimuFundCheckOrderPo> simuFundCheckOrderPos = simuFundCheckOrderPoMapper.selectByExample(simuFundCheckOrderPoExample);
        return CollectionUtils.isEmpty(simuFundCheckOrderPos)? null : simuFundCheckOrderPos.get(0);
    }

    /**
     *
     * getByDealDtlNo:根据交易明细订单号查询私募对账订单信息
     *
     * @param dealDtlNo
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午1:33:20
     */
    public List<SimuFundCheckOrderPo> getByDealDtlNo(String dealDtlNo) {
        if (StringUtils.isEmpty(dealDtlNo)) {
            return null;
        }

        return simuFundCheckOrderPoMapper.selectByDealDtlNo(dealDtlNo);
    }

    /**
     *
     * getByContractNo:根据后台订单号查询私募对账订单信息
     * qiang.tang
     * 2020-06-18
     * 关联交易
     */
    public List<SimuFundCheckOrderPo> getByContractNo(String contractNo) {
        if (StringUtils.isEmpty(contractNo)) {
            return null;
        }

        return simuFundCheckOrderPoMapper.getByContractNo(contractNo);
    }

    /**
     * getCheckOrdersToSubmit:查询所有私募待上报的对账订单
     *
     * @param tradeDt
     * @param submitRetrieveCtr
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午2:07:19
     */
    public List<SimuFundCheckOrderPo> getCheckOrdersToSubmit(String tradeDt, Date now, String submitRetrieveCtr) {
        return simuFundCheckOrderPoMapper.selectCheckOrdersToSubmit(tradeDt, now, submitRetrieveCtr);
    }

    /**
     *
     * updateAppFlagOrCompFlag:根据上报申请订单号更新对账订单中的交易申请标记或者交易对账标记
     *
     * @param submitDealNo
     *            上报申请订单号
     * @param txAppFlag
     *            交易申请标记
     * @param txCompFlag
     *            对账标记
     * @param cancelOrderSrc
     *            撤销来源
     * @param oldUpdateDtm
     *            最后更新时间戳
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午2:21:06
     */
    public int updateAppFlagOrCompFlag(String submitDealNo, String txAppFlag, String txCompFlag, String submitAppFlag, String cancelOrderSrc,
                                       Date oldUpdateDtm) {
        Date now = new Date();
        return simuFundCheckOrderPoMapper.updateAppFlagOrCompFlagBySubmitDealNo(submitDealNo, txAppFlag, txCompFlag, submitAppFlag, cancelOrderSrc, now,
                oldUpdateDtm);
    }

    /**
     *
     * updateByPrimaryKeySelective:根据上报订单号更新公募对账订单信息，
     *
     * @param order
     *            更新的对账订单信息
     * @return 更新的记录数
     * <AUTHOR>
     * @date 2017年7月7日 下午2:24:36
     */
    public int updateBySubmitDealNoSelective(SimuFundCheckOrderPo order) {
        if (order == null) {
            return 0;
        }
        order.setUpdateDtm(new Date());
        SimuFundCheckOrderPoExample simuFundCheckOrderPoExample = new SimuFundCheckOrderPoExample();
        simuFundCheckOrderPoExample.createCriteria().andSubmitDealNoEqualTo(order.getSubmitDealNo());
        return simuFundCheckOrderPoMapper.updateByExampleSelective(order,simuFundCheckOrderPoExample);
    }

    /***
     *
     * getDealOrderByTaDateAndTxCompFlag:(根据TA交易日和对账标记分页查询)
     *
     * @param taTradeDt
     * @param txCompFlag
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午7:43:11
     */
    public Page<DealCheckOrderVo> getDealOrderByTaDateAndTxCompFlag(String taTradeDt, List<String> filterFundCodeList, String txCompFlag, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }
        return simuFundCheckOrderPoMapper.selectDealCheckOrderByTaTradeDateAndTxCompFlag(filterFundCodeList, taTradeDt, txCompFlag);
    }

    public BigDecimal sumAppAmt(String taDate, String txCompFlag) {
        return simuFundCheckOrderPoMapper.sumAppAmt(taDate, txCompFlag);
    }

    /***
     *
     * getCountFundCheckOrderForConsole:(中控台-交易申请统计)
     *
     * @param queryCondition
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 下午3:13:17
     */
    public SimuFundCheckOrderPo getCountSimuFundCheckOrderForConsole(QuerySimuFundCheckOrderCondition queryCondition) {
        SimuFundCheckOrderPo condition = new SimuFundCheckOrderPo();
        BeanUtils.copyProperties(queryCondition, condition);

        return simuFundCheckOrderPoMapper.selectCountSimuFundCheckOrderForConsole(condition, queryCondition.getAppDateStart(), queryCondition.getAppDateEnd(),
                queryCondition.getTaTradeDtStart(), queryCondition.getTaTradeDtEnd(), queryCondition.getFilterFundCodeList());

    }

    public Page<SimuFundCheckOrderPo> getSimuFundCheckOrderWithPage(String taCode, String tradeDt, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }
        SimuFundCheckOrderPo condition = new SimuFundCheckOrderPo();
        condition.setTaCode(taCode);
        condition.setTradeDt(tradeDt);
        return simuFundCheckOrderPoMapper.selectSimuFundCheckOrderWithPage(condition);
    }

    public Page<SimuFundCheckOrderDto> getSimuFundCheckOrderForConsole(QuerySimuFundCheckOrderCondition queryCondition, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }

        SimuFundCheckOrderPo condition = new SimuFundCheckOrderPo();
        BeanUtils.copyProperties(queryCondition, condition);
        return simuFundCheckOrderPoMapper.selectSimuFundCheckOrderForConsole(condition, queryCondition.getAppDateStart(), queryCondition.getAppDateEnd(),
                queryCondition.getTaTradeDtStart(), queryCondition.getTaTradeDtEnd(), queryCondition.getMergeSubmitFlag(), queryCondition.getFilterFundCodeList());
    }

    /***
     *
     * countDealNotCompDeal:(交易未对账交易)
     *
     * @param taTradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2017年7月20日 上午10:51:51
     */
    public Integer countDealNotCompDeal(String taTradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.countDealNotCompDeal(taTradeDt, taCodes);
    }

    public List<String> queryDealNotCompDeal(String taTradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.queryDealNotCompDeal(taTradeDt, taCodes);
    }

    /**
     *
     * getListDealNotCompDeal:交易未对账列表
     * @param taTradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午8:53:26
     */
    public List<SimuFundCheckOrderPo> getListDealNotCompDeal(String taTradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.selectListDealNotCheck(taTradeDt, taCodes);
    }





    /**
     * getByAckImportDtAndProductChannel:(查询需生成份额确认书的当日确认订单)
     *
     * @param ackImportDt
     * @return
     * <AUTHOR>
     * @date 2018年2月12日 下午2:30:10
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<SimuFundCheckOrderDto> getNeedGenVolConfirmBookByAckImportDt(final String ackImportDt) {
        return simuFundCheckOrderPoMapper.selectNeedGenVolConfirmBookByAckImportDt(ackImportDt);
    }

    public SimuFundCheckOrderPo getTotalAmountByMainDealNo(String mainDealNo) {
        return simuFundCheckOrderPoMapper.selectTotalAmountByMainDealNo(mainDealNo);
    }

    /**
     *
     * getShareMergeCheckOrdersToSubmit:查询所有待上报的对账订单
     *
     * @param startDtm
     *            开始时间
     * @return
     * <AUTHOR>
     * @date 2016年9月14日 上午11:05:41
     */
    public List<ShareMergeOrderVo> getShareMergeCheckOrdersToSubmit(Date startDtm) {
        return this.simuFundCheckOrderPoMapper.selectShareMergeCheckOrdersToSubmit(startDtm);
    }


    /**
     *
     * getShareMergeInSubmitDealNo:查询份额合并/迁移转入上报订单号(根据身份证号进行唯一主键展示)
     *
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2018年4月2日 上午10:56:30
     */
    public Map<String, SimuFundCheckOrderPo> getShareMergeInCheckOrder(String dealNo) {
        return simuFundCheckOrderPoMapper.selectShareMergeInCheckOrder(dealNo);
    }

    /**
     *
     * getShareMergeOutByDealNo:根据订单号查询份额合并/迁移转出上报信息
     *
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2018年5月18日 下午7:26:27
     */
    public List<SimuFundCheckOrderPo> getShareMergeOutByDealNo(String dealNo) {
        return simuFundCheckOrderPoMapper.selectShareMergeOutByDealNo(dealNo);
    }


    public int updateBatch(List<SimuFundCheckOrderPo> list, String submitAppFlag) {
        int count = 0;
        for (SimuFundCheckOrderPo po : list) {
            count += simuFundCheckOrderPoMapper.updateByDealNo(po.getDealNo(), submitAppFlag);
        }
        return count;
    }


    public int updateRetrieveDtm(String submitDealNo, Date retrieveDtm) {
        return simuFundCheckOrderPoMapper.updateRetrieveDtm(submitDealNo, retrieveDtm);
    }


    public Map<String, SimuFundCheckOrderPo> getMapByDisDealAckNo(List<String> disDealAckNoList) {
        Map<String, SimuFundCheckOrderPo> map = Maps.newHashMap();
        Map<String, SimuFundCheckOrderPo> detailMap = simuFundCheckOrderPoMapper.selectFundCheckByDisDealAckNo(disDealAckNoList);
        map.putAll(detailMap);
        return map;
    }


    public List<String> querySubmitingOrReSubmit(String tradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.querySubmitingOrReSubmit(tradeDt, taCodes);
    }

    public List<String> querySubmittingOrReSubmit(String signStartDt, String endTradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.querySubmitingOrReSubmitByDt(signStartDt, endTradeDt, taCodes);
    }

    /**
     *
     * getListSubmitingOrReSubmit:上报中or重新上报的订单
     *
     * @param tradeDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 上午10:53:59
     */
    public List<SimuFundCheckOrderPo> getListSubmitingOrReSubmit(String tradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.selectSubmitingOrReSubmit(tradeDt, taCodes);
    }

    public List<SimuFundCheckOrderPo> getListSubmitingOrReSubmitByDt(String signStartDt, String tradeDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.selectSubmitingOrReSubmitByDt(signStartDt, tradeDt, taCodes);
    }

    /**
     *
     * getCountHighTradeAckDataForConsole:交易确认统计
     *
     * @param queryCondition
     * @return
     * <AUTHOR>
     * @date 2018年6月20日 下午7:11:31
     */
    public HighTradeAckDataVo getCountHighTradeAckDataForConsole(QueryHighFundAckFileRecCondition queryCondition) {
        SimuFundCheckOrderPo condition = new SimuFundCheckOrderPo();
        BeanUtils.copyProperties(queryCondition, condition);

        return simuFundCheckOrderPoMapper.selectCountHighTradeAckDataForConsole(condition, queryCondition.getAckDtStart(), queryCondition.getAckDtEnd(), queryCondition.getFilterFundCodeList());
    }

    /**
     *
     * countSubmit:查询已上报的数量
     * @param tradeDt
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月19日 下午5:07:09
     */
    public Integer countSubmit(String tradeDt, String taCode, String productChannel) {
        return simuFundCheckOrderPoMapper.countSubmit(tradeDt, taCode, productChannel);
    }

    /**
     *
     * querySubmitDtlList:查询上报明细
     * @param tradeDt
     * @param taCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 上午9:58:15
     */
    public List<HighDealSubmitDtlVo> querySubmitDtlList(String tradeDt, String taCode, String productChannel) {
        return simuFundCheckOrderPoMapper.selectSubmitDtl(tradeDt, taCode, productChannel);
    }

    /**
     *
     * getCheckOrdersToManualSubmit:可上报数据查询
     * @param tradeDt
     * @param taCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 下午1:30:30
     */
    public List<SimuFundCheckOrderManualSubmitVo> getCheckOrdersToManualSubmit(String tradeDt, String taCode, String productChannel) {
        return simuFundCheckOrderPoMapper.selectCheckOrdersToManualSubmit(tradeDt, taCode, productChannel);
    }
    public List<SimuFundCheckOrderManualSubmitVo> getByDealNoList(List<String> dealDtlNoList) {
        return simuFundCheckOrderPoMapper.selectCheckOrdersToManualSubmitByDealDtlNo(dealDtlNoList);
    }
    /**
     * 根据主订单号查询可上报订单
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.SimuFundCheckOrderManualSubmitVo>
     * @author: huaqiang.liu
     * @date: 2021/3/11 17:37
     * @since JDK 1.8
     */
    public List<SimuFundCheckOrderManualSubmitVo> getCheckOrdersToManualSubmitByMainDealNo(String mainDealNo) {
        return simuFundCheckOrderPoMapper.selectCheckOrdersToManualSubmitByMainDealNo(mainDealNo);
    }
    /**
     *
     * getCheckOrdersByAckDtAndTaCode:根据确认导入日期和TACODE查询
     * @param tradeDt
     * @param fundCodes
     * @return
     * <AUTHOR>
     * @date 2019年2月2日 下午3:09:20
     */
    public List<SimuFundCheckOrderPo> getCheckOrdersByAckDtAndFundCodes(String tradeDt, List<String> fundCodes) {
        return simuFundCheckOrderPoMapper.selectCheckOrdersByAckDtAndFundCodes(tradeDt, fundCodes);
    }

    /**
     *
     * getPurchaseAckSucc:查询确认成功,到期赎回订单
     * @param ackImportDt 导入确认日期
     */
    public List<ExpireRedeemVo> getPurchaseAckSuccForRedeemExpire(String ackImportDt) {
        return simuFundCheckOrderPoMapper.selectPurchaseAckSuccForRedeemExpire(ackImportDt);
    }

    /**
     *
     * getShareTransferCheckOrdersToSubmit:份额迁移
     * @param startDtm
     * @return
     * <AUTHOR>
     * @date 2019年5月22日 下午3:01:31
     */
    public List<SimuFundCheckOrderPo> getShareTransferCheckOrdersToSubmit(Date startDtm) {
        return simuFundCheckOrderPoMapper.selectShareTransferCheckOrdersToSubmit(startDtm);
    }

    public int batchUpdateForceRedeemMemo(List<SimuFundCheckOrderPo> forceRedeemMemoList) {
        return simuFundCheckOrderPoMapper.batchUpdateForceRedeemMemo(forceRedeemMemoList);
    }

    /**
     * @description:查询确认多回款方向数据
     * @param ackDt
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo>
     * @author: chuanguang.tang
     * @date: 2021/7/23 15:28
     * @since JDK 1.8
     */
    public List<SimuFundCheckOrderPo> getAckRefundList(String ackDt) {
        return simuFundCheckOrderPoMapper.selectAckRefundList(ackDt);
    }

    public List<SimuFundCheckOrderPo> getSimuFundCheckOrderBySplit(HighDealOrderDtlPo params) {
        return simuFundCheckOrderPoMapper.getSimuFundCheckOrderBySplit(params.getTxAcctNo(), params.getFundCode(), params.getCpAcctNo(), params.getAckDt());
    }


    /**
     *
     * getHighTradeAckDataForConsole:查询高端确认数据
     * @param queryCondition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2018年6月20日 下午7:09:18
     */
    public Page<HighTradeAckDataVo> getHighTradeAckDataForConsole(QueryHighFundAckFileRecCondition queryCondition, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }

        SimuFundCheckOrderPo condition = new SimuFundCheckOrderPo();
        BeanUtils.copyProperties(queryCondition, condition);

        return simuFundCheckOrderPoMapper.selectHighTradeAckDataForConsole(condition, queryCondition.getAckDtStart(),
                queryCondition.getAckDtEnd(), queryCondition.getMergeSubmitFlag(), queryCondition.getFilterFundCodeList());
    }

    public Integer countSubmitingOrReSubmit(String submitTaDt, List<String> taCodes) {
        return simuFundCheckOrderPoMapper.countSubmitingOrReSubmit(submitTaDt, taCodes);
    }

    public int updateSubmitFlagWithForce(String dealNo, SimuFundCheckOrderPo simuFundCheckOrderPo) {
        return simuFundCheckOrderPoMapper.updateSubmitFlagWithForce(dealNo, simuFundCheckOrderPo);
    }

    public List<SimuFundCheckOrderPo> selectByDealDtlNo(String dealDtlNo) {
        return simuFundCheckOrderPoMapper.selectByDealDtlNo(dealDtlNo);
    }

    public void insertSelective(SimuFundCheckOrderPo checkPo) {
        simuFundCheckOrderPoMapper.insertSelective(checkPo);
    }

    public void updateSubmitTaDtByDealNo(String dealNo, String submitTaDt, String submitTaDtOld, Date now) {
        simuFundCheckOrderPoMapper.updateSubmitTaDtByDealNo(dealNo, submitTaDt, submitTaDtOld, now);
    }

    public int updateNotNeedSubmitByDealDtlNo(SimuFundCheckOrderDto simuFundCheckOrderDto) {
        return simuFundCheckOrderPoMapper.updateNotNeedSubmitByDealDtlNo(simuFundCheckOrderDto);
    }

    public int updateReSubmittingByDealDtlNo(SimuFundCheckOrderDto simuFundCheckOrderDto) {
        return simuFundCheckOrderPoMapper.updateReSubmittingByDealDtlNo(simuFundCheckOrderDto);
    }

    public void insert(SimuFundCheckOrderDto simuFundCheckOrderDto) {
        simuFundCheckOrderPoMapper.insert(simuFundCheckOrderDto);
    }

    public int updateRedeemDirection(String dealNo, String redeemDirection) {
       return simuFundCheckOrderPoMapper.updateRedeemDirection(dealNo,redeemDirection);
    }

    public int updateAppFlagOrCompFlagBySubmitDealNo(String submitDealNo, String checkTxAppFlag, String checkTxCompFlag, String submitAppFlag, String cancelOrderSrc, Date now, Date updateDtm) {
        return simuFundCheckOrderPoMapper.updateAppFlagOrCompFlagBySubmitDealNo(submitDealNo, checkTxAppFlag, checkTxCompFlag, submitAppFlag, cancelOrderSrc, now, updateDtm);
    }

    public List<SimuFundCheckOrderPo> selectShareMergeCheckOrdersToRefershResult(Date startDtm) {
        return simuFundCheckOrderPoMapper.selectShareMergeCheckOrdersToRefershResult(startDtm);
    }

    public List<SimuFundCheckOrderPo> selectByTradeDtAndTaCode(String tradeDt, String taCode) {
        return simuFundCheckOrderPoMapper.selectByTradeDtAndTaCode(tradeDt, taCode);
    }

    public List<DealPmtVo> getBySubmitDealNos(List<String> submitDealNos) {
        return simuFundCheckOrderPoMapper.getBySubmitDealNos(submitDealNos);
    }

    public Collection<? extends OrderSumGroupTaVo> getOrderSumGroupTa(String taTradeDt, List<String> taCodeList) {
        return simuFundCheckOrderPoMapper.getOrderSumGroupTa(taTradeDt, taCodeList);
    }
}
